package show

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"vlab/app/api/imdb"
	"vlab/app/common/dbs"
	classFieldDao "vlab/app/dao/content_class_field"
	showDao "vlab/app/dao/content_show"
	externalIdsDao "vlab/app/dao/content_show_external_ids"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// ExternalIDsService 外部ID服务
type ExternalIDsService struct {
	showRepo        showDao.Repo
	externalIDsRepo externalIdsDao.Repo
	classFieldRepo  classFieldDao.Repo
	imdbService     *imdb.MatchService
	debug           bool                // 调试模式
	genreCache      map[uint64][]string // genre缓存，key为showID
	cacheMutex      sync.RWMutex        // 缓存读写锁
	logger          *log.Logger         // 日志记录器

	// 断路器相关字段
	circuitBreaker *EnhancedCircuitBreaker
	// 取消函数用于清理goroutine
	cancel context.CancelFunc
}

// NewExternalIDsService 创建外部ID服务
func NewExternalIDsService() *ExternalIDsService {
	// 创建断路器配置
	cbConfig := &CircuitBreakerConfig{
		FailureThreshold:      50, // 调整失败阈值为50，提高容错性
		SuccessThreshold:      2,
		Timeout:               30 * time.Second,
		MaxHalfOpenRequests:   3,
		WindowSize:            100,
		FailureRateThreshold:  0.5,
		MinRequestsToCalcRate: 10,
		UseExponentialBackoff: true,
		MaxBackoffTime:        5 * time.Minute,
	}

	logger := log.New(os.Stdout, "[ExternalIDsService] ", log.LstdFlags|log.Lmicroseconds)

	service := &ExternalIDsService{
		showRepo:        showDao.GetRepo(),
		externalIDsRepo: externalIdsDao.GetRepo(),
		classFieldRepo:  classFieldDao.GetRepo(),
		imdbService:     imdb.NewMatchService(nil),
		debug:           false,
		genreCache:      make(map[uint64][]string),
		logger:          logger,
		circuitBreaker:  NewEnhancedCircuitBreaker(cbConfig, logger),
	}

	// 启动时记录服务初始化信息
	service.logger.Printf("[INFO] 🚀 ExternalIDsService 初始化完成")
	service.logger.Printf("[INFO] 📊 断路器配置: 失败阈值=%d, 成功阈值=%d, 超时=%v, 半开请求数=%d",
		cbConfig.FailureThreshold, cbConfig.SuccessThreshold,
		cbConfig.Timeout, cbConfig.MaxHalfOpenRequests)
	service.logger.Printf("[INFO] 📊 滑动窗口=%d, 失败率阈值=%.1f%%, 指数退避=%v",
		cbConfig.WindowSize, cbConfig.FailureRateThreshold*100, cbConfig.UseExponentialBackoff)

	return service
}

// NewExternalIDsServiceWithDebug 创建带调试模式的外部ID服务
func NewExternalIDsServiceWithDebug(debug bool) *ExternalIDsService {
	service := NewExternalIDsService()
	service.debug = debug
	if debug && service.imdbService != nil {
		// 传递debug标志给IMDB服务
		service.imdbService.SetDebug(debug)
	}
	return service
}

// SyncExternalIDs 同步单个剧集的外部IDs
func (s *ExternalIDsService) SyncExternalIDs(ctx *gin.Context, showID uint64) error {
	// 获取剧集信息
	showList, err := s.showRepo.FindByFilter(ctx, &showDao.Filter{
		ID: showID,
	})
	if err != nil {
		return fmt.Errorf("failed to find show: %w", err)
	}
	if len(showList) == 0 {
		return fmt.Errorf("show not found: %d", showID)
	}
	show := showList[0]

	// Debug: 记录剧集基本信息
	if s.debug {
		// TODO: 使用logger输出
		s.logger.Printf("[DEBUG] 开始同步剧集 ID=%d\n", showID)
		s.logger.Printf("[DEBUG] 剧集名称: %s\n", show.Name)
		s.logger.Printf("[DEBUG] 剧集简介: %.100s...\n", show.Overview)
		s.logger.Printf("[DEBUG] AirDate: %s\n", show.AirDate)
		s.logger.Printf("[DEBUG] PresentationTime: %d\n", show.PresentationTime)
		s.logger.Printf("[DEBUG] ContentType: %v\n", show.ContentType)
		s.logger.Printf("[DEBUG] Langs: %s\n", show.Langs)
	}

	// 准备匹配参数
	contentType := s.determineContentType(show)
	genres := s.extractGenres(ctx, show)
	language := s.extractLanguage(show)
	year := s.extractYear(show)

	// Debug: 记录提取的参数
	if s.debug {
		// TODO: 使用logger输出
		s.logger.Printf("[DEBUG] 提取的参数:\n")
		s.logger.Printf("  - ContentType: %s\n", contentType)
		s.logger.Printf("  - Genres: %v\n", genres)
		s.logger.Printf("  - Language: %s\n", language)
		s.logger.Printf("  - Year: %d\n", year)
		s.logger.Printf("[DEBUG] 准备调用IMDB API...\n")
	}

	// 调用IMDB API进行匹配
	// 使用正确的context.Context类型，并添加显式超时控制
	requestCtx := s.getSafeContext(ctx)
	timeoutCtx, cancel := context.WithTimeout(requestCtx, 90*time.Second)
	defer cancel()

	if s.debug {
		s.logger.Printf("[DEBUG] 开始调用IMDB API，超时时间：90秒")
	}

	mediaIDs, err := s.imdbService.MatchShow(
		timeoutCtx,
		show.Name,
		year,
		show.Overview,
		language,
		genres,
		contentType,
	)

	// Debug: 记录API响应
	if s.debug {
		if err != nil {
			s.logger.Printf("[DEBUG] API调用失败: %v\n", err)
		} else if mediaIDs == nil {
			s.logger.Printf("[DEBUG] API返回无匹配结果 (mediaIDs=nil)\n")
		} else {
			s.logger.Printf("[DEBUG] API返回匹配结果:\n")
			s.logger.Printf("  - IMDB: %s\n", mediaIDs.IMDB)
			s.logger.Printf("  - TMDB: %d\n", mediaIDs.TMDB)
			s.logger.Printf("  - Trakt: %d\n", mediaIDs.Trakt)
			s.logger.Printf("  - Slug: %s\n", mediaIDs.Slug)
		}
	}

	if err != nil {
		return fmt.Errorf("failed to match show %d: %w", showID, err)
	}

	// 没有找到匹配
	if mediaIDs == nil {
		if s.debug {
			s.logger.Printf("[DEBUG] 保存无匹配记录\n")
		}
		// No match found, save no match record
		return s.saveNoMatchRecord(ctx, showID)
	}

	// 保存匹配结果
	if s.debug {
		s.logger.Printf("[DEBUG] 保存匹配结果\n")
	}
	return s.saveExternalIDs(ctx, showID, mediaIDs, contentType)
}

// SyncExternalIDsWithCache 同步单个剧集的外部IDs（使用缓存版本）
func (s *ExternalIDsService) SyncExternalIDsWithCache(ctx *gin.Context, showID uint64) error {
	// 使用新的断路器API检查状态
	if s.circuitBreaker.IsOpen() {
		stats := s.circuitBreaker.GetStats()
		s.logger.Printf("[WARN] 断路器开启，跳过剧集 %d | 状态: %v", showID, stats["state"])
		return fmt.Errorf("circuit breaker is open, skipping show %d", showID)
	}
	// 获取剧集信息
	if s.debug {
		s.logger.Printf("[DEBUG] 开始查询剧集信息，showID: %d", showID)
	}

	// ❗ 修复：简化超时控制，直接使用原始context
	showList, err := s.showRepo.FindByFilter(ctx, &showDao.Filter{
		ID: showID,
	})
	if err != nil {
		if s.debug {
			s.logger.Printf("[DEBUG] 查询剧集信息失败: %v", err)
		}
		return fmt.Errorf("failed to find show: %w", err)
	}
	if len(showList) == 0 {
		return fmt.Errorf("show not found: %d", showID)
	}
	show := showList[0]

	// Debug: 记录剧集基本信息
	if s.debug {
		s.logger.Printf("[DEBUG] 成功查询到剧集信息")
		s.logger.Printf("[DEBUG] 开始同步剧集 ID=%d (使用缓存)\n", showID)
		s.logger.Printf("[DEBUG] 剧集名称: %s\n", show.Name)
	}

	// 准备匹配参数
	contentType := s.determineContentType(show)
	genres := s.extractGenresWithCache(ctx, show) // 使用缓存版本
	language := s.extractLanguage(show)
	year := s.extractYear(show)

	// Debug: 记录提取的参数
	if s.debug {
		s.logger.Printf("[DEBUG] 提取的参数:\n")
		s.logger.Printf("  - ContentType: %s\n", contentType)
		s.logger.Printf("  - Genres: %v\n", genres)
		s.logger.Printf("  - Language: %s\n", language)
		s.logger.Printf("  - Year: %d\n", year)
	}

	// 调用IMDB API进行匹配
	// 🔧 修复：使用更保守的超时策略，避免资源长时间占用
	requestCtx := s.getSafeContext(ctx)
	timeoutCtx, cancel := context.WithTimeout(requestCtx, 45*time.Second) // 减少到45秒
	defer cancel()

	if s.debug {
		s.logger.Printf("[DEBUG] 开始调用IMDB API，超时时间：45秒")
	}

	mediaIDs, err := s.imdbService.MatchShow(
		timeoutCtx,
		show.Name,
		year,
		show.Overview,
		language,
		genres,
		contentType,
	)

	if s.debug {
		if err != nil {
			s.logger.Printf("[DEBUG] API调用失败: %v", err)
		} else {
			s.logger.Printf("[DEBUG] API调用成功")
		}
	}

	if err != nil {
		// 记录失败，断路器内部处理线程安全
		s.circuitBreaker.RecordResult(false, err)
		s.logger.Printf("[ERROR] API调用失败 | 剧集ID: %d | 错误: %v", showID, err)

		return fmt.Errorf("failed to match show %d: %w", showID, err)
	}

	// 记录成功
	s.circuitBreaker.RecordResult(true, nil)

	// 没有找到匹配
	if mediaIDs == nil {
		if s.debug {
			s.logger.Printf("[DEBUG] 保存无匹配记录\n")
		}
		return s.saveNoMatchRecordWithTimeout(ctx, showID)
	}

	// 保存匹配结果
	if s.debug {
		s.logger.Printf("[DEBUG] 保存匹配结果\n")
	}
	return s.saveExternalIDsWithTimeout(ctx, showID, mediaIDs, contentType)
}

// BatchSyncExternalIDs 批量同步剧集的外部IDs（并发版本）
func (s *ExternalIDsService) BatchSyncExternalIDs(ctx *gin.Context, showIDs []uint64) (map[uint64]error, error) {
	// 性能优化：减少worker数量避免API限流
	return s.BatchSyncExternalIDsWithWorkers(ctx, showIDs, 3) // 减少到3个worker
}

// BatchSyncExternalIDsWithWorkers 使用指定数量的worker并发同步剧集的外部IDs
func (s *ExternalIDsService) BatchSyncExternalIDsWithWorkers(ctx *gin.Context, showIDs []uint64, numWorkers int) (map[uint64]error, error) {
	if numWorkers <= 0 {
		numWorkers = 5 // 默认值改为3
	}

	// 最多使用5个worker，避免API限流
	if numWorkers > 5 {
		numWorkers = 5
	}

	// 如果任务数量很少，减少worker数量
	if len(showIDs) < numWorkers {
		numWorkers = len(showIDs)
	}

	// 检查断路器状态
	stats := s.circuitBreaker.GetStats()
	state := stats["state"].(string)
	failures := stats["consecutive_failures"].(int32)
	lastFailureTime := stats["last_failure_time"].(time.Time)

	if state == "OPEN" {
		timeSinceFailure := time.Since(lastFailureTime)
		s.logger.Printf("[WARN] ⚠️  断路器已开启！状态: %s, 失败次数: %d, 距离上次失败: %.1fs",
			state, failures, timeSinceFailure.Seconds())

		// ❗ 关键修复：改进goroutine生命周期管理
		if s.cancel != nil {
			s.cancel() // 取消之前的goroutine
			// 短暂等待确保之前的goroutine退出
			time.Sleep(100 * time.Millisecond)
		}

		// 限制监控时间，避免长时间阻塞
		monitCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		s.cancel = cancel

		// 使用更安全的goroutine启动方式
		go func() {
			defer func() {
				if r := recover(); r != nil {
					s.logger.Printf("[ERROR] 断路器监控goroutine panic: %v", r)
				}
			}()
			s.monitorCircuitBreakerRecovery(monitCtx)
		}()
	} else if state == "HALF_OPEN" {
		s.logger.Printf("[INFO] 🔄 断路器处于半开状态，正在测试服务恢复")
	} else {
		s.logger.Printf("[INFO] ✅ 断路器状态正常（%s），开始处理任务", state)
	}

	// 性能优化：预加载所有genres到缓存
	if s.debug {
		s.logger.Printf("[DEBUG] 预加载 %d 个剧集的genres信息...\n", len(showIDs))
	}
	if err := s.batchLoadGenres(ctx, showIDs); err != nil {
		// 预加载失败不影响继续执行，只是性能会慢一些
		if s.debug {
			s.logger.Printf("[DEBUG] 预加载genres失败: %v，将使用单个查询\n", err)
		}
	}

	// 使用sync.Map来存储结果，保证线程安全
	var resultsMap sync.Map
	var processedCount int32

	// 创建任务channel
	tasks := make(chan uint64, len(showIDs))

	// 创建WaitGroup等待所有worker完成
	var wg sync.WaitGroup

	// 启动worker goroutines
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			// 每个worker从channel中获取任务并处理
			for showID := range tasks {
				// 增加处理计数
				currentCount := atomic.AddInt32(&processedCount, 1)

				// 定期输出进度 - 更频繁的进度更新
				if currentCount%5 == 0 || s.debug {
					s.logger.Printf("[进度] 已处理 %d/%d 个剧集\n", currentCount, len(showIDs))
				}

				// 记录开始处理的日志
				if s.debug {
					s.logger.Printf("[DEBUG] Worker %d: 开始处理剧集 %d...", workerID, showID)
				}

				// ❗ 关键修复：为每个任务设置更短的超时控制
				taskTimeout := 20 * time.Second
				if s.debug {
					taskTimeout = 30 * time.Second // 调试模式稍长一些
				}
				taskCtx, taskCancel := context.WithTimeout(context.Background(), taskTimeout)
				taskDone := make(chan error, 1)

				// 在goroutine中执行任务，传递正确的context
				go func() {
					defer func() {
						// 防止panic导致goroutine泄漏
						if r := recover(); r != nil {
							s.logger.Printf("[ERROR] Worker %d: 处理剧集 %d 时发生panic: %v", workerID, showID, r)
							taskDone <- fmt.Errorf("worker panic: %v", r)
						}
					}()

					// 创建带有超时context的gin.Context
					ginCtx := helper.GenGinCtx()
					ginCtx.Request = ginCtx.Request.WithContext(taskCtx)

					err := s.SyncExternalIDsWithCache(ginCtx, showID)
					taskDone <- err
				}()

				// 等待任务完成或超时
				var err error
				startTime := time.Now()
				select {
				case err = <-taskDone:
					// 任务正常完成
					if s.debug {
						elapsed := time.Since(startTime)
						s.logger.Printf("[DEBUG] Worker %d: 剧集 %d 处理完成，耗时: %.2fs", workerID, showID, elapsed.Seconds())
					}
				case <-taskCtx.Done():
					// 任务超时
					elapsed := time.Since(startTime)
					err = fmt.Errorf("task timeout after %.1fs", elapsed.Seconds())
					s.logger.Printf("[ERROR] Worker %d: 处理剧集 %d 超时 (%.1fs)，可能的原因：网络阻塞或API无响应", workerID, showID, elapsed.Seconds())
				}
				taskCancel()

				if err != nil {
					// 只记录有错误的结果
					resultsMap.Store(showID, err)

					// 区分断路器跳过和实际错误
					if strings.Contains(err.Error(), "circuit breaker is open") {
						s.logger.Printf("[WARN] Worker %d: 断路器开启，跳过剧集 %d", workerID, showID)
					} else if strings.Contains(err.Error(), "timeout") {
						s.logger.Printf("[ERROR] Worker %d: 处理剧集 %d 超时: %v", workerID, showID, err)
					} else {
						s.logger.Printf("[ERROR] Worker %d: 处理剧集 %d 失败: %v", workerID, showID, err)
					}
				} else if s.debug {
					s.logger.Printf("[DEBUG] Worker %d: 成功处理剧集 %d", workerID, showID)
				}

				// 每处理完一个，短暂延迟避免API限流
				time.Sleep(500 * time.Millisecond)
			}
		}(i)
	}

	// 将所有任务放入channel
	for _, showID := range showIDs {
		tasks <- showID
	}

	// 关闭channel，通知worker没有更多任务
	close(tasks)

	// 等待所有worker完成
	wg.Wait()

	// 将sync.Map转换为普通map返回
	results := make(map[uint64]error)
	resultsMap.Range(func(key, value interface{}) bool {
		showID := key.(uint64)
		err := value.(error)
		results[showID] = err
		return true
	})

	if s.debug {
		s.logger.Printf("[DEBUG] Batch sync completed. Total: %d, Errors: %d\n", len(showIDs), len(results))
	}

	return results, nil
}

// GetExternalIDsByShowID 根据ShowID获取外部IDs
func (s *ExternalIDsService) GetExternalIDsByShowID(ctx *gin.Context, showID uint64) (*externalIdsDao.Model, error) {
	return s.externalIDsRepo.FindByShowID(ctx, showID)
}

// GetExternalIDsByShowIDs 批量获取外部IDs
func (s *ExternalIDsService) GetExternalIDsByShowIDs(ctx *gin.Context, showIDs []uint64) (externalIdsDao.ModelList, error) {
	return s.externalIDsRepo.FindByShowIDs(ctx, showIDs)
}

// SearchByIMDBID 根据IMDB ID查找ShowID
func (s *ExternalIDsService) SearchByIMDBID(ctx *gin.Context, imdbID string) (uint64, error) {
	model, err := s.externalIDsRepo.FindByImdbID(ctx, imdbID)
	if err != nil {
		return 0, err
	}
	if model == nil {
		return 0, nil
	}
	return model.ShowID, nil
}

// SearchByTMDBID 根据TMDB ID查找ShowID
func (s *ExternalIDsService) SearchByTMDBID(ctx *gin.Context, tmdbID uint64) (uint64, error) {
	model, err := s.externalIDsRepo.FindByTmdbID(ctx, tmdbID)
	if err != nil {
		return 0, err
	}
	if model == nil {
		return 0, nil
	}
	return model.ShowID, nil
}

// UpdateExternalIDs 更新外部IDs
func (s *ExternalIDsService) UpdateExternalIDs(ctx *gin.Context, showID uint64, updates map[string]interface{}) error {
	// 查找现有记录
	existing, err := s.externalIDsRepo.FindByShowID(ctx, showID)
	if err != nil {
		return err
	}

	if existing == nil {
		// 创建新记录
		model := &externalIdsDao.Model{
			ShowID: showID,
			Source: externalIdsDao.SourceManual.String(),
		}
		s.applyUpdates(model, updates)
		return s.externalIDsRepo.Create(ctx, model)
	}

	// 更新现有记录
	s.applyUpdates(existing, updates)
	return s.externalIDsRepo.Update(ctx, existing)
}

// 辅助方法

// determineContentType 判断内容类型
func (s *ExternalIDsService) determineContentType(show *showDao.Show) string {
	switch show.ContentType {
	case showDao.ContentTypeMovie:
		return "movie"
	case showDao.ContentTypeTV:
		return "show"
	default:
		return imdb.DetermineContentType(show.Name, show.Overview)
	}
}

// extractGenres 提取类型标签
func (s *ExternalIDsService) extractGenres(ctx *gin.Context, show *showDao.Show) []string {
	// 查询show关联的genre信息 (class_id = 5 表示genre类型)
	showWithClassList, err := s.showRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
		ShowID:  show.ID,
		ClassID: 5, // genre类型
	})
	if err != nil || len(showWithClassList) == 0 {
		// 查询失败或没有genre信息，返回空数组
		if s.debug {
			s.logger.Printf("[DEBUG] 未找到show %d 的genre信息: %v\n", show.ID, err)
		}
		return []string{}
	}

	// 提取field IDs
	fieldIDs := showWithClassList.GetFieldIDs()
	if len(fieldIDs) == 0 {
		return []string{}
	}

	// 查询genre具体信息
	genreFields, err := s.classFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
		IDs:     fieldIDs,
		ClassID: 5, // genre类型
		Status:  dbs.StatusEnable,
	})
	if err != nil || len(genreFields) == 0 {
		if s.debug {
			s.logger.Printf("[DEBUG] 查询genre字段失败: %v\n", err)
		}
		return []string{}
	}

	// 提取genre名称
	genres := make([]string, 0, len(genreFields))
	for _, field := range genreFields {
		if field.Name != "" {
			// 优先使用英文名称，如果Name是英文的话
			genres = append(genres, field.Name)
		} else if field.NameKey != "" {
			// 如果没有Name，使用NameKey
			genres = append(genres, field.NameKey)
		}
	}

	if s.debug {
		s.logger.Printf("[DEBUG] Show %d genres: %v\n", show.ID, genres)
	}

	// 如果没有找到任何genre，返回空数组而不是unknown
	return genres
}

// batchLoadGenres 批量加载genres到缓存
func (s *ExternalIDsService) batchLoadGenres(ctx *gin.Context, showIDs []uint64) error {
	if len(showIDs) == 0 {
		return nil
	}

	// 查询所有show的genre关联信息
	showWithClassList, err := s.showRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
		ShowIDs: showIDs,
		ClassID: 5, // genre类型
	})
	if err != nil {
		return fmt.Errorf("批量查询genre关联失败: %w", err)
	}

	if len(showWithClassList) == 0 {
		// 没有任何genre信息，为所有show设置空数组
		s.cacheMutex.Lock()
		for _, showID := range showIDs {
			s.genreCache[showID] = []string{}
		}
		s.cacheMutex.Unlock()
		return nil
	}

	// 按showID分组
	showGenreMap := showWithClassList.GetShowIDMap()

	// 提取所有field IDs
	allFieldIDs := showWithClassList.GetFieldIDs()
	if len(allFieldIDs) == 0 {
		return nil
	}

	// 批量查询所有genre字段信息
	genreFields, err := s.classFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
		IDs:     allFieldIDs,
		ClassID: 5,
		//Status:  dbs.StatusEnable,
	})
	if err != nil {
		return fmt.Errorf("批量查询genre字段失败: %w", err)
	}

	// 创建fieldID到genre名称的映射
	fieldNameMap := make(map[uint64]string)
	for _, field := range genreFields {
		if field.Name != "" {
			fieldNameMap[field.ID] = field.Name
		} else if field.NameKey != "" {
			fieldNameMap[field.ID] = field.NameKey
		}
	}

	// 构建缓存
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	for _, showID := range showIDs {
		genres := []string{}
		if showGenres, ok := showGenreMap[showID]; ok {
			for _, sg := range showGenres {
				if name, ok := fieldNameMap[sg.FieldID]; ok {
					genres = append(genres, name)
				}
			}
		}
		s.genreCache[showID] = genres

		if s.debug {
			s.logger.Printf("[DEBUG] 缓存Show %d genres: %v\n", showID, genres)
		}
	}

	return nil
}

// extractGenresWithCache 从缓存提取genres，如果没有则查询
func (s *ExternalIDsService) extractGenresWithCache(ctx *gin.Context, show *showDao.Show) []string {
	// ❗ 修复：简化缓存读取，直接使用锁
	s.cacheMutex.RLock()
	if genres, ok := s.genreCache[show.ID]; ok {
		s.cacheMutex.RUnlock()
		if s.debug {
			s.logger.Printf("[DEBUG] 从缓存获取Show %d genres: %v\n", show.ID, genres)
		}
		return genres
	}
	s.cacheMutex.RUnlock()

	if s.debug {
		s.logger.Printf("[DEBUG] 缓存未命中，查询Show %d 的genres\n", show.ID)
	}
	// 缓存未命中，执行查询
	return s.extractGenres(ctx, show)
}

// extractLanguage 提取语言
func (s *ExternalIDsService) extractLanguage(show *showDao.Show) string {
	if show.Langs != "" {
		// 取第一个语言代码
		langs := strings.Split(show.Langs, ",")
		if len(langs) > 0 {
			return strings.TrimSpace(langs[0])
		}
	}
	return "en"
}

// extractYear 提取年份
func (s *ExternalIDsService) extractYear(show *showDao.Show) int {
	if show.PresentationTime > 0 {
		return int(show.PresentationTime)
	}

	// 从AirDate中提取年份
	if show.AirDate != "" && len(show.AirDate) >= 4 {
		// 尝试解析前4个字符作为年份
		year := 0
		fmt.Sscanf(show.AirDate[:4], "%d", &year)
		if year > 1900 && year < 2100 {
			return year
		}
	}

	return 2020 // 默认值
}

// saveExternalIDs 保存外部IDs
func (s *ExternalIDsService) saveExternalIDs(ctx *gin.Context, showID uint64, mediaIDs *imdb.MediaIDs, contentType string) error {
	if s.debug {
		s.logger.Printf("[DEBUG] 开始保存external IDs，showID: %d", showID)
	}

	model := &externalIdsDao.Model{
		ShowID:     showID,
		MatchType:  contentType,
		MatchScore: floatPtr(100.0), // 成功匹配默认100分
		IsMatch:    2,
		Source:     externalIdsDao.SourceIMDBAPI.String(),
	}

	// 设置外部IDs
	if mediaIDs != nil {
		if mediaIDs.Trakt > 0 {
			model.TraktID = uint64Ptr(mediaIDs.Trakt)
		}
		model.Slug = mediaIDs.Slug
		model.ImdbID = mediaIDs.IMDB
		if mediaIDs.TMDB > 0 {
			model.TmdbID = uint64Ptr(mediaIDs.TMDB)
		}

		// 保存原始响应
		rawData := map[string]interface{}{
			"trakt": mediaIDs.Trakt,
			"slug":  mediaIDs.Slug,
			"imdb":  mediaIDs.IMDB,
			"tmdb":  mediaIDs.TMDB,
		}
		rawResponse := externalIdsDao.RawResponseMap(rawData)
		model.RawResponse = &rawResponse
	}

	if s.debug {
		s.logger.Printf("[DEBUG] 执行数据库UpsertByShowID操作...")
	}

	// ❗ 修复：简化数据库操作，使用原始context
	err := s.externalIDsRepo.UpsertByShowID(ctx, model)

	if s.debug {
		if err != nil {
			s.logger.Printf("[DEBUG] 数据库操作失败: %v", err)
		} else {
			s.logger.Printf("[DEBUG] 数据库操作成功完成")
		}
	}

	return err
}

// saveExternalIDsWithTimeout 保存外部IDs（带超时控制）
func (s *ExternalIDsService) saveExternalIDsWithTimeout(ctx *gin.Context, showID uint64, mediaIDs *imdb.MediaIDs, contentType string) error {
	if s.debug {
		s.logger.Printf("[DEBUG] 开始保存external IDs，showID: %d", showID)
	}

	now := time.Now()
	nowPtr := (*dbs.LocalTime)(&now)
	tmdbID := mediaIDs.TMDB
	traktID := mediaIDs.Trakt

	model := &externalIdsDao.Model{
		ShowID:    showID,
		ImdbID:    mediaIDs.IMDB,
		TmdbID:    &tmdbID,
		TraktID:   &traktID,
		Slug:      mediaIDs.Slug,
		MatchType: contentType,
		IsMatch:   1,
		Source:    "imdb_api",
	}
	model.CreatedAt = nowPtr
	model.UpdatedAt = nowPtr

	// 验证必要字段
	if model.ImdbID == "" && (model.TmdbID == nil || *model.TmdbID == 0) && (model.TraktID == nil || *model.TraktID == 0) {
		return fmt.Errorf("no valid external IDs found")
	}

	if s.debug {
		s.logger.Printf("[DEBUG] External IDs to save:")
		s.logger.Printf("  - IMDB: %s", model.ImdbID)
		if model.TmdbID != nil {
			s.logger.Printf("  - TMDB: %d", *model.TmdbID)
		}
		if model.TraktID != nil {
			s.logger.Printf("  - Trakt: %d", *model.TraktID)
		}
		s.logger.Printf("  - Slug: %s", model.Slug)
		s.logger.Printf("  - MatchType: %s", model.MatchType)
	}

	if s.debug {
		s.logger.Printf("[DEBUG] 执行数据库UpsertByShowID操作...")
	}

	// 🔧 关键修复：大幅减少数据库操作超时，快速失败
	requestCtx := s.getSafeContext(ctx)
	dbCtx, cancel := context.WithTimeout(requestCtx, 5*time.Second) // 保持5秒，快速失败
	defer cancel()

	// 创建带有超时context的gin.Context
	dbGinCtx := helper.GenGinCtx()
	dbGinCtx.Request = dbGinCtx.Request.WithContext(dbCtx)

	err := s.externalIDsRepo.UpsertByShowID(dbGinCtx, model)

	if s.debug {
		if err != nil {
			s.logger.Printf("[DEBUG] 数据库操作失败: %v", err)
		} else {
			s.logger.Printf("[DEBUG] 数据库操作成功完成")
		}
	}

	return err
}

// saveNoMatchRecordWithTimeout 保存无匹配记录（带超时控制）
func (s *ExternalIDsService) saveNoMatchRecordWithTimeout(ctx *gin.Context, showID uint64) error {
	if s.debug {
		s.logger.Printf("[DEBUG] 开始保存无匹配记录，showID: %d", showID)
	}

	now := time.Now()
	nowPtr := (*dbs.LocalTime)(&now)

	model := &externalIdsDao.Model{
		ShowID:  showID,
		IsMatch: 0, // 无匹配
		Source:  "imdb_api",
	}
	model.CreatedAt = nowPtr
	model.UpdatedAt = nowPtr

	if s.debug {
		s.logger.Printf("[DEBUG] 执行数据库UpsertByShowID操作（无匹配记录）...")
	}

	// ❗ 为数据库操作设置超时（通过context传递）
	requestCtx := s.getSafeContext(ctx)
	dbCtx, cancel := context.WithTimeout(requestCtx, 15*time.Second)
	defer cancel()

	// 创建带有超时context的gin.Context
	dbGinCtx := helper.GenGinCtx()
	dbGinCtx.Request = dbGinCtx.Request.WithContext(dbCtx)

	err := s.externalIDsRepo.UpsertByShowID(dbGinCtx, model)

	if s.debug {
		if err != nil {
			s.logger.Printf("[DEBUG] 数据库操作失败: %v", err)
		} else {
			s.logger.Printf("[DEBUG] 无匹配记录保存成功")
		}
	}

	return err
}

// saveNoMatchRecord 保存无匹配记录
func (s *ExternalIDsService) saveNoMatchRecord(ctx *gin.Context, showID uint64) error {
	if s.debug {
		s.logger.Printf("[DEBUG] 开始保存无匹配记录，showID: %d", showID)
	}

	model := &externalIdsDao.Model{
		ShowID:      showID,
		MatchScore:  floatPtr(0),
		IsMatch:     1,
		Source:      externalIdsDao.SourceIMDBAPI.String(),
		MatchReason: "No suitable match found",
	}

	if s.debug {
		s.logger.Printf("[DEBUG] 执行数据库UpsertByShowID操作（无匹配记录）...")
	}

	// ❗ 修复：简化数据库操作，使用原始context
	err := s.externalIDsRepo.UpsertByShowID(ctx, model)

	if s.debug {
		if err != nil {
			s.logger.Printf("[DEBUG] 数据库操作失败: %v", err)
		} else {
			s.logger.Printf("[DEBUG] 无匹配记录保存成功")
		}
	}

	return err
}

// applyUpdates 应用更新
func (s *ExternalIDsService) applyUpdates(model *externalIdsDao.Model, updates map[string]interface{}) {
	for key, value := range updates {
		switch key {
		case "imdb_id":
			if v, ok := value.(string); ok {
				model.ImdbID = v
			}
		case "tmdb_id":
			if v, ok := value.(uint64); ok {
				model.TmdbID = &v
			} else if v, ok := value.(int); ok {
				tmdbID := uint64(v)
				model.TmdbID = &tmdbID
			}
		case "trakt_id":
			if v, ok := value.(uint64); ok {
				model.TraktID = &v
			} else if v, ok := value.(int); ok {
				traktID := uint64(v)
				model.TraktID = &traktID
			}
		case "slug":
			if v, ok := value.(string); ok {
				model.Slug = v
			}
		case "match_type":
			if v, ok := value.(string); ok {
				model.MatchType = v
			}
		}
	}
}

// 辅助函数
func floatPtr(f float64) *float64 {
	return &f
}

func uint32Ptr(u uint32) *uint32 {
	return &u
}

func uint64Ptr(u uint64) *uint64 {
	return &u
}

// GetUnsyncedCount 获取未同步剧集的总数
func (s *ExternalIDsService) GetUnsyncedCount(ctx *gin.Context) (int64, error) {
	// 获取所有剧集总数
	allShowsCount, err := s.showRepo.CountByFilter(ctx, &showDao.Filter{})
	if err != nil {
		return 0, fmt.Errorf("failed to count all shows: %w", err)
	}

	// 获取已同步的剧集数量
	syncedCount, err := s.externalIDsRepo.Count(ctx, &externalIdsDao.Filter{
		IsMatch: 2, // 已成功匹配的
	})
	if err != nil {
		return 0, fmt.Errorf("failed to count synced shows: %w", err)
	}

	// 计算未同步数量
	unsyncedCount := allShowsCount - syncedCount
	if unsyncedCount < 0 {
		unsyncedCount = 0
	}

	return unsyncedCount, nil
}

// GetUnsyncedCountInRange 获取指定ID范围内的未同步剧集总数
func (s *ExternalIDsService) GetUnsyncedCountInRange(ctx *gin.Context, startID, endID uint64) (int64, error) {
	// 构建过滤条件
	filter := &showDao.Filter{}
	if startID > 0 {
		filter.MinID = startID - 1 // MinID 是"大于"的语义，所以要-1以包含startID
	}
	if endID > 0 {
		filter.MaxID = endID + 1 // MaxID 是"小于"的语义，所以要+1以包含endID
	}

	// 获取范围内的剧集总数
	allShowsCount, err := s.showRepo.CountByFilter(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count shows in range %d-%d: %w", startID, endID, err)
	}

	// 获取范围内的剧集ID
	allShows, err := s.showRepo.FindXidsByFilter(ctx, filter, dbs.PluckID)
	if err != nil {
		return 0, fmt.Errorf("failed to get shows in range %d-%d: %w", startID, endID, err)
	}

	if len(allShows) == 0 {
		return 0, nil
	}

	// 查询这些ID中已同步的
	syncedShows, err := s.externalIDsRepo.FindByShowIDs(ctx, allShows)
	if err != nil {
		return 0, fmt.Errorf("failed to get synced shows: %w", err)
	}

	// 计算未同步的数量
	syncedCount := len(syncedShows)
	unsyncedCount := allShowsCount - int64(syncedCount)

	return unsyncedCount, nil
}

// GetUnsyncedCountAfterID 获取大于指定ID的未同步剧集总数
func (s *ExternalIDsService) GetUnsyncedCountAfterID(ctx *gin.Context, lastID uint64) (int64, error) {
	// 获取大于 lastID 的剧集总数
	allShowsCount, err := s.showRepo.CountByFilter(ctx, &showDao.Filter{
		MinID: lastID,
	})
	if err != nil {
		return 0, fmt.Errorf("failed to count shows after ID %d: %w", lastID, err)
	}

	// 获取这些剧集中已同步的数量
	// 需要先获取所有大于lastID的剧集ID，然后查询已同步的
	allShows, err := s.showRepo.FindXidsByFilter(ctx, &showDao.Filter{
		MinID: lastID,
	}, dbs.PluckID)
	if err != nil {
		return 0, fmt.Errorf("failed to get shows after ID %d: %w", lastID, err)
	}

	if len(allShows) == 0 {
		return 0, nil
	}

	// 查询这些ID中已同步的
	syncedShows, err := s.externalIDsRepo.FindByShowIDs(ctx, allShows)
	if err != nil {
		return 0, fmt.Errorf("failed to get synced shows: %w", err)
	}

	syncedCount := int64(0)
	for _, show := range syncedShows {
		if show.IsMatch == 2 {
			syncedCount++
		}
	}

	// 计算未同步数量
	unsyncedCount := allShowsCount - syncedCount
	if unsyncedCount < 0 {
		unsyncedCount = 0
	}

	return unsyncedCount, nil
}

// GetUnsyncedShowIDsPaginated 分页获取未同步的剧集ID
func (s *ExternalIDsService) GetUnsyncedShowIDsPaginated(ctx *gin.Context, offset, limit int) ([]uint64, error) {
	if limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000 // 最大限制
	}

	// 获取所有剧集ID（分页）
	allShows, err := s.showRepo.FindXidsByFilter(ctx, &showDao.Filter{}, dbs.PluckID)
	if err != nil {
		return nil, fmt.Errorf("failed to get shows: %w", err)
	}

	// 计算实际的分页范围
	start := offset
	end := offset + limit
	if start >= len(allShows) {
		return []uint64{}, nil // 超出范围，返回空
	}
	if end > len(allShows) {
		end = len(allShows)
	}

	// 获取当前页的剧集ID
	pageShows := allShows[start:end]
	if len(pageShows) == 0 {
		return []uint64{}, nil
	}

	// 查询这些剧集的同步状态
	syncedShows, err := s.externalIDsRepo.FindByShowIDs(ctx, pageShows)
	if err != nil {
		return nil, fmt.Errorf("failed to get synced shows: %w", err)
	}

	// 创建已同步ID的map
	syncedMap := make(map[uint64]bool)
	for _, show := range syncedShows {
		if show.IsMatch == 2 {
			syncedMap[show.ShowID] = true
		}
	}

	// 找出未同步的剧集ID
	var unsyncedIDs []uint64
	for _, showID := range pageShows {
		if !syncedMap[showID] {
			unsyncedIDs = append(unsyncedIDs, showID)
		}
	}

	return unsyncedIDs, nil
}

// GetUnsyncedShowIDsPaginatedInRange 分页获取指定ID范围内的未同步剧集
func (s *ExternalIDsService) GetUnsyncedShowIDsPaginatedInRange(ctx *gin.Context, startID, endID uint64, offset, limit int) ([]uint64, error) {
	if limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000 // 最大限制
	}

	// 构建过滤条件
	filter := &showDao.Filter{}
	if startID > 0 {
		filter.MinID = startID - 1 // MinID 是"大于"的语义，所以要-1以包含startID
	}
	if endID > 0 {
		filter.MaxID = endID + 1 // MaxID 是"小于"的语义，所以要+1以包含endID
	}

	// 获取范围内的剧集ID
	allShows, err := s.showRepo.FindXidsByFilter(ctx, filter, dbs.PluckID)
	if err != nil {
		return nil, fmt.Errorf("failed to get shows in range %d-%d: %w", startID, endID, err)
	}

	if len(allShows) == 0 {
		return nil, nil
	}

	// 查询已同步的剧集
	syncedShows, err := s.externalIDsRepo.FindByShowIDs(ctx, allShows)
	if err != nil {
		return nil, fmt.Errorf("failed to get synced shows: %w", err)
	}

	// 创建已同步的ID集合
	syncedMap := make(map[uint64]bool)
	for _, s := range syncedShows {
		syncedMap[s.ShowID] = true
	}

	// 过滤出未同步的ID
	var unsyncedIDs []uint64
	for _, id := range allShows {
		if !syncedMap[id] {
			unsyncedIDs = append(unsyncedIDs, id)
		}
	}

	// 实现分页
	total := len(unsyncedIDs)
	if offset >= total {
		return []uint64{}, nil
	}

	end := offset + limit
	if end > total {
		end = total
	}

	return unsyncedIDs[offset:end], nil
}

// GetUnsyncedShowIDsPaginatedAfterID 分页获取大于指定ID的未同步剧集
func (s *ExternalIDsService) GetUnsyncedShowIDsPaginatedAfterID(ctx *gin.Context, lastID uint64, offset, limit int) ([]uint64, error) {
	if limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000 // 最大限制
	}

	// 获取大于lastID的剧集ID
	allShows, err := s.showRepo.FindXidsByFilter(ctx, &showDao.Filter{
		MinID: lastID,
	}, dbs.PluckID)
	if err != nil {
		return nil, fmt.Errorf("failed to get shows after ID %d: %w", lastID, err)
	}

	// 计算实际的分页范围
	start := offset
	end := offset + limit
	if start >= len(allShows) {
		return []uint64{}, nil // 超出范围，返回空
	}
	if end > len(allShows) {
		end = len(allShows)
	}

	// 获取当前页的剧集ID
	pageShows := allShows[start:end]
	if len(pageShows) == 0 {
		return []uint64{}, nil
	}

	// 查询这些剧集的同步状态
	syncedShows, err := s.externalIDsRepo.FindByShowIDs(ctx, pageShows)
	if err != nil {
		return nil, fmt.Errorf("failed to get synced shows: %w", err)
	}

	// 创建已同步ID的map
	syncedMap := make(map[uint64]bool)
	for _, show := range syncedShows {
		if show.IsMatch == 2 {
			syncedMap[show.ShowID] = true
		}
	}

	// 找出未同步的剧集ID
	var unsyncedIDs []uint64
	for _, showID := range pageShows {
		if !syncedMap[showID] {
			unsyncedIDs = append(unsyncedIDs, showID)
		}
	}

	return unsyncedIDs, nil
}

// GetUnsyncedShowIDs 获取未同步的剧集ID列表（优化版本，减少内存使用）
func (s *ExternalIDsService) GetUnsyncedShowIDs(ctx *gin.Context, limit int) ([]uint64, error) {
	// 重要：为了避免内存溢出，我们强制限制最大返回数量
	maxLimit := 1000 // 减少到1000个，避免内存问题
	if limit <= 0 || limit > maxLimit {
		limit = maxLimit
	}

	if s.debug {
		s.logger.Printf("[DEBUG] GetUnsyncedShowIDs开始，限制: %d\n", limit)
	}

	// 优化：分批获取剧集ID，避免一次性加载太多
	batchSize := 5000 // 每批获取5000个
	offset := 0
	var unsyncedIDs []uint64

	for {
		if s.debug {
			s.logger.Printf("[DEBUG] 获取剧集ID，offset: %d, batchSize: %d\n", offset, batchSize)
		}

		// 分批获取剧集ID
		// 注意：这里仍然使用原来的方法，但限制了数量
		allShows, err := s.showRepo.FindXidsByFilter(ctx, &showDao.Filter{}, dbs.PluckID)
		if err != nil {
			return nil, fmt.Errorf("failed to get shows: %w", err)
		}

		// 处理当前批次
		start := offset
		end := offset + batchSize
		if start >= len(allShows) {
			break // 没有更多数据
		}
		if end > len(allShows) {
			end = len(allShows)
		}

		batchShows := allShows[start:end]

		if s.debug {
			s.logger.Printf("[DEBUG] 当前批次包含 %d 个剧集\n", len(batchShows))
		}

		// 获取这批剧集的同步状态
		if len(batchShows) > 0 {
			// 查询已同步的剧集
			syncedShows, err := s.externalIDsRepo.FindByShowIDs(ctx, batchShows)
			if err != nil {
				return nil, fmt.Errorf("failed to get synced shows: %w", err)
			}

			// 创建已同步ID的map
			syncedMap := make(map[uint64]bool)
			for _, show := range syncedShows {
				if show.IsMatch == 2 {
					syncedMap[show.ShowID] = true
				}
			}

			// 找出未同步的剧集ID
			for _, showID := range batchShows {
				if !syncedMap[showID] {
					unsyncedIDs = append(unsyncedIDs, showID)
					// 达到限制就返回
					if len(unsyncedIDs) >= limit {
						if s.debug {
							s.logger.Printf("[DEBUG] 达到限制 %d，返回未同步ID列表\n", limit)
						}
						return unsyncedIDs[:limit], nil
					}
				}
			}
		}

		// 如果已经处理了所有数据，退出
		if end >= len(allShows) {
			break
		}

		offset += batchSize
	}

	if s.debug {
		s.logger.Printf("[DEBUG] GetUnsyncedShowIDs完成，找到 %d 个未同步的剧集\n", len(unsyncedIDs))
	}

	return unsyncedIDs, nil
}

// GetStatistics 获取外部ID同步统计
func (s *ExternalIDsService) GetStatistics(ctx *gin.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总记录数
	totalFilter := &externalIdsDao.Filter{}
	total, err := s.externalIDsRepo.Count(ctx, totalFilter)
	if err != nil {
		return nil, err
	}
	stats["total"] = total

	// 成功匹配数
	isMatch := uint8(2)
	matchedFilter := &externalIdsDao.Filter{
		IsMatch: isMatch,
	}
	matched, err := s.externalIDsRepo.Count(ctx, matchedFilter)
	if err != nil {
		return nil, err
	}
	stats["matched"] = matched

	// 未匹配数
	notMatch := uint8(1)
	notMatchedFilter := &externalIdsDao.Filter{
		IsMatch: notMatch,
	}
	notMatched, err := s.externalIDsRepo.Count(ctx, notMatchedFilter)
	if err != nil {
		return nil, err
	}
	stats["not_matched"] = notMatched

	// 按类型统计
	movieFilter := &externalIdsDao.Filter{
		MatchType: "movie",
	}
	movieCount, _ := s.externalIDsRepo.Count(ctx, movieFilter)

	showFilter := &externalIdsDao.Filter{
		MatchType: "show",
	}
	showCount, _ := s.externalIDsRepo.Count(ctx, showFilter)

	stats["by_type"] = map[string]int64{
		"movie": movieCount,
		"show":  showCount,
	}

	// 按来源统计
	imdbAPIFilter := &externalIdsDao.Filter{
		Source: externalIdsDao.SourceIMDBAPI.String(),
	}
	imdbAPICount, _ := s.externalIDsRepo.Count(ctx, imdbAPIFilter)

	manualFilter := &externalIdsDao.Filter{
		Source: externalIdsDao.SourceManual.String(),
	}
	manualCount, _ := s.externalIDsRepo.Count(ctx, manualFilter)

	stats["by_source"] = map[string]int64{
		"imdb_api": imdbAPICount,
		"manual":   manualCount,
	}

	return stats, nil
}

// getSafeContext 安全地从gin.Context中获取context.Context
// 在命令行工具中，gin.Context的Request字段可能为nil
func (s *ExternalIDsService) getSafeContext(ctx *gin.Context) context.Context {
	if ctx.Request != nil {
		return ctx.Request.Context()
	}
	// 在命令行工具中使用background context
	return context.Background()
}

// getGenresFromCache 从缓存中获取genres
func (s *ExternalIDsService) getGenresFromCache(showID uint64) []string {
	s.cacheMutex.RLock()
	defer s.cacheMutex.RUnlock()
	return s.genreCache[showID]
}

// setGenresCache 设置genres缓存
func (s *ExternalIDsService) setGenresCache(showID uint64, genres []string) {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	s.genreCache[showID] = genres
}

// monitorCircuitBreakerRecovery 监控断路器恢复状态
func (s *ExternalIDsService) monitorCircuitBreakerRecovery(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Printf("[INFO] 停止监控断路器恢复")
			return
		case <-ticker.C:
			state := s.circuitBreaker.GetState()
			if state == StateClosed {
				s.logger.Printf("[INFO] 🎉 断路器已自动恢复！可以继续处理请求")
				return
			} else if state == StateHalfOpen {
				s.logger.Printf("[INFO] 🔄 断路器正在测试恢复...")
			}
		}
	}
}

// ResetCircuitBreaker 重置断路器状态（提供给外部调用）
func (s *ExternalIDsService) ResetCircuitBreaker() {
	s.circuitBreaker.Reset()
	s.logger.Printf("[INFO] 🔄 断路器已手动重置")
}

// PerformHealthCheck 执行健康检查，测试IMDB API连通性
func (s *ExternalIDsService) PerformHealthCheck() error {
	s.logger.Printf("[INFO] 🔍 正在执行IMDB API健康检查...")

	// 使用更轻量的连接测试，避免实际API调用
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 检查服务是否初始化
	if s.imdbService == nil {
		s.logger.Printf("[ERROR] ❌ IMDB服务未初始化")
		return fmt.Errorf("IMDB service not initialized")
	}

	// 测试网络连接
	if err := s.testNetworkConnectivity(ctx); err != nil {
		s.logger.Printf("[ERROR] ❌ 网络连接测试失败: %v", err)
		return fmt.Errorf("network connectivity test failed: %w", err)
	}

	s.logger.Printf("[INFO] ✅ IMDB服务初始化正常")
	s.logger.Printf("[INFO] 📡 网络连接检查通过")
	s.logger.Printf("[INFO] 🎉 健康检查完成，服务可正常使用")

	return nil
}

// testNetworkConnectivity 测试网络连通性
func (s *ExternalIDsService) testNetworkConnectivity(ctx context.Context) error {
	// 测试到API服务器的TCP连接
	dialer := &net.Dialer{
		Timeout: 10 * time.Second,
	}

	conn, err := dialer.DialContext(ctx, "tcp", "118.196.31.23:5310")
	if err != nil {
		return fmt.Errorf("无法连接到API服务器: %w", err)
	}
	defer conn.Close()

	return nil
}
