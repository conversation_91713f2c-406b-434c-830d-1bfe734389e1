#!/bin/bash

# 简单的并发超时修复验证脚本
# 专注于验证核心修复是否有效

echo "=========================================="
echo "🔧 简单并发超时修复验证"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检查API连接数
check_api_connections() {
    echo -e "${YELLOW}检查API连接数...${NC}"
    local connections=$(netstat -an 2>/dev/null | grep "*************:5310" | wc -l)
    echo "当前API连接数: $connections"
    
    if [ "$connections" -le 4 ]; then
        echo -e "${GREEN}✅ API连接数正常 (≤4)${NC}"
        return 0
    else
        echo -e "${RED}❌ API连接数过多 (>4)${NC}"
        return 1
    fi
}

# 测试单进程
test_single_process() {
    echo -e "${YELLOW}测试单进程运行...${NC}"
    
    timeout 30 ./external_ids_sync --cmd sync-batch --start-id 1 --end-id 10 --safe > /tmp/single_test.log 2>&1 &
    local pid=$!
    
    sleep 20
    
    if kill -0 $pid 2>/dev/null; then
        echo -e "${GREEN}✅ 单进程运行正常${NC}"
        kill $pid 2>/dev/null
        return 0
    else
        echo -e "${RED}❌ 单进程运行异常${NC}"
        return 1
    fi
}

# 测试双进程
test_dual_process() {
    echo -e "${YELLOW}测试双进程并发运行...${NC}"
    
    # 启动第一个进程
    timeout 60 ./external_ids_sync --cmd sync-batch --start-id 1 --end-id 20 --safe > /tmp/process1.log 2>&1 &
    local pid1=$!
    
    # 等待3秒后启动第二个进程
    sleep 3
    timeout 60 ./external_ids_sync --cmd sync-batch --start-id 21 --end-id 40 --safe > /tmp/process2.log 2>&1 &
    local pid2=$!
    
    echo "进程1 PID: $pid1"
    echo "进程2 PID: $pid2"
    
    # 监控30秒
    local start_time=$(date +%s)
    local timeout_errors=0
    
    while [ $(($(date +%s) - start_time)) -lt 30 ]; do
        sleep 5
        
        # 检查API连接数
        check_api_connections
        
        # 检查超时错误
        local current_timeouts=$(grep -c "timeout" /tmp/process1.log /tmp/process2.log 2>/dev/null || echo 0)
        if [ "$current_timeouts" -gt "$timeout_errors" ]; then
            echo -e "${RED}⚠️  发现超时错误: $((current_timeouts - timeout_errors))个${NC}"
            timeout_errors=$current_timeouts
        fi
        
        # 检查进程状态
        local p1_running=$(kill -0 $pid1 2>/dev/null && echo "true" || echo "false")
        local p2_running=$(kill -0 $pid2 2>/dev/null && echo "true" || echo "false")
        echo "进程状态: P1=$p1_running, P2=$p2_running"
        
        # 如果都结束了，提前退出
        if [ "$p1_running" = "false" ] && [ "$p2_running" = "false" ]; then
            break
        fi
    done
    
    # 清理进程
    kill $pid1 $pid2 2>/dev/null
    
    # 分析结果
    echo ""
    echo "=== 测试结果分析 ==="
    echo "超时错误总数: $timeout_errors"
    
    if [ "$timeout_errors" -eq 0 ]; then
        echo -e "${GREEN}🎉 双进程测试成功，无超时错误！${NC}"
        return 0
    elif [ "$timeout_errors" -le 2 ]; then
        echo -e "${YELLOW}⚠️  双进程测试基本成功，少量超时错误${NC}"
        return 0
    else
        echo -e "${RED}❌ 双进程测试失败，超时错误过多${NC}"
        return 1
    fi
}

# 显示修复摘要
show_fix_summary() {
    echo ""
    echo "=========================================="
    echo "🔧 已实施的修复措施"
    echo "=========================================="
    echo "1. HTTP连接数大幅减少："
    echo "   - MaxConnsPerHost: 3 → 2"
    echo "   - MaxIdleConnsPerHost: 2 → 1"
    echo "   - 两进程总连接数: ≤4个"
    echo ""
    echo "2. 连接超时优化："
    echo "   - 连接超时: 20秒 → 10秒"
    echo "   - 响应超时: 60秒 → 30秒"
    echo "   - 空闲超时: 30秒 → 15秒"
    echo ""
    echo "3. 连接泄漏修复："
    echo "   - 每次API调用后立即清理空闲连接"
    echo ""
    echo "4. 数据库连接优化："
    echo "   - 连接池大小: 100 → 50"
    echo "   - 空闲连接: 32 → 16"
    echo ""
    echo "5. 超时策略调整："
    echo "   - API调用超时: 120秒 → 60秒"
    echo "   - 数据库操作保持5秒快速失败"
}

# 提供使用建议
show_usage_recommendations() {
    echo ""
    echo "=========================================="
    echo "💡 使用建议"
    echo "=========================================="
    echo "1. 避免同时启动多个进程，建议间隔30秒："
    echo "   ./external_ids_sync --cmd sync-batch --start-id 1 --end-id 1000 --safe &"
    echo "   sleep 30"
    echo "   ./external_ids_sync --cmd sync-batch --start-id 1001 --end-id 2000 --safe &"
    echo ""
    echo "2. 监控API连接数："
    echo "   watch 'netstat -an | grep *************:5310 | wc -l'"
    echo ""
    echo "3. 如果仍有问题，进一步减少连接数："
    echo "   - 考虑将MaxConnsPerHost改为1"
    echo "   - 增加进程间启动延迟到60秒"
    echo ""
    echo "4. 检查API服务器状态："
    echo "   curl -I http://*************:5310"
}

# 主函数
main() {
    show_fix_summary
    
    echo ""
    echo "开始验证修复效果..."
    echo ""
    
    # 检查前置条件
    if [ ! -f "./external_ids_sync" ]; then
        echo -e "${RED}❌ external_ids_sync 可执行文件不存在${NC}"
        exit 1
    fi
    
    # 测试单进程
    if ! test_single_process; then
        echo -e "${RED}❌ 单进程测试失败，请检查基础配置${NC}"
        exit 1
    fi
    
    echo ""
    
    # 测试双进程
    if test_dual_process; then
        echo ""
        echo -e "${GREEN}🎉 修复验证成功！${NC}"
        echo -e "${GREEN}并发超时问题已基本解决${NC}"
    else
        echo ""
        echo -e "${YELLOW}⚠️  修复效果有限，可能需要进一步调整${NC}"
    fi
    
    show_usage_recommendations
}

# 运行测试
main "$@"
