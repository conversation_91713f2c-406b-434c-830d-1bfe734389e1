package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"vlab/app/common/dbs"
	externalIdsDao "vlab/app/dao/content_show_external_ids"
	showService "vlab/app/service/show"
	"vlab/config"
	"vlab/pkg/helper"
	"vlab/pkg/monitor"

	"github.com/gin-gonic/gin"
)

// 日志相关变量
var (
	logFile      *os.File
	logger       *log.Logger
	logToFile    bool       // 是否输出到文件
	logFilePath  string     // 日志文件路径
	logCounter   int        // 日志计数器
	logSyncMutex sync.Mutex // 日志同步锁

	// 监控系统变量
	globalMonitor *monitor.CmdMonitor // 全局监控器
)

// smartLogSync 智能日志同步，根据配置决定是否立即刷盘
func smartLogSync() {
	if logFile == nil {
		return
	}

	logSyncMutex.Lock()
	defer logSyncMutex.Unlock()

	logCounter++

	// ❗ 改进：更智能的同步策略，减少IO阻塞
	shouldSync := false
	if logSyncInterval == 0 {
		shouldSync = true // 立即同步模式
	} else if fastMode {
		// 快速模式：更大的同步间隔，减少IO
		shouldSync = logCounter%200 == 0
	} else {
		// 普通模式：使用配置的间隔
		shouldSync = logCounter%logSyncInterval == 0
	}

	// 额外策略：每5分钟强制同步一次，避免丢失重要日志
	if !shouldSync {
		lastSyncTime := time.Now().Unix() / 300 // 5分钟间隔
		currentTime := time.Now().Unix() / 300
		if lastSyncTime != currentTime {
			shouldSync = true
		}
	}

	if shouldSync {
		// ❗ 非阻塞同步：在goroutine中执行，避免阻塞主线程
		go func() {
			if err := logFile.Sync(); err != nil {
				// 同步失败时直接输出到stderr，避免递归
				fmt.Fprintf(os.Stderr, "[LOG SYNC ERROR] %v\n", err)
			}
		}()
	}
}

// LogAdapter 日志适配器，用于传递给service
type LogAdapter struct {
	logger *log.Logger
	debug  bool
}

// Debugf 调试日志
func (l *LogAdapter) Debugf(format string, args ...interface{}) {
	if l.debug && l.logger != nil {
		l.logger.Printf("[DEBUG] "+format, args...)
	}
}

// Infof 信息日志
func (l *LogAdapter) Infof(format string, args ...interface{}) {
	if l.logger != nil {
		l.logger.Printf("[INFO] "+format, args...)
	}
}

// Errorf 错误日志
func (l *LogAdapter) Errorf(format string, args ...interface{}) {
	if l.logger != nil {
		l.logger.Printf("[ERROR] "+format, args...)
	}
}

// 命令行参数
var (
	configFile      string
	command         string
	showID          uint64
	showIDs         string
	imdbID          string
	tmdbID          uint64
	limit           int
	offset          int
	forceUpdate     bool
	verbose         bool
	syncAll         bool
	limitBatch      int
	pageSize        int    // 每批处理数量
	startID         uint64 // 起始剧集ID（包含）
	endID           uint64 // 结束剧集ID（包含）
	lastID          uint64 // 从指定ID开始同步（已弃用）
	skipExisting    bool
	autoConfirm     bool
	debug           bool // 调试模式
	resetCircuitBrk bool // 重置断路器
	fastMode        bool // 快速模式：减少日志输出
	logSyncInterval int  // 日志同步间隔（条数）
	safeMode        bool // 安全模式：启用所有防卡住修复
	aggressiveMode  bool // 激进模式：使用最短超时，快速跳过问题剧集
	monitorTest     bool // 测试监控系统
)

func init() {
	// 基础参数
	flag.StringVar(&configFile, "config", "./config/local.ini", "配置文件路径")
	flag.StringVar(&command, "cmd", "", "命令: sync-one, sync-batch, query, stats, search, health-check")
	flag.BoolVar(&verbose, "verbose", false, "显示详细信息")
	flag.BoolVar(&forceUpdate, "force", false, "强制更新已存在的记录")
	flag.BoolVar(&debug, "debug", false, "启用调试模式，显示详细的API请求和响应")
	flag.BoolVar(&resetCircuitBrk, "reset-circuit-breaker", false, "重置断路器状态（用于强制恢复）")
	flag.BoolVar(&fastMode, "fast", false, "快速模式：减少日志输出，提升性能")
	flag.IntVar(&logSyncInterval, "log-sync-interval", 50, "日志同步间隔（每N条日志同步一次磁盘，0=立即同步）")
	flag.BoolVar(&logToFile, "log-to-file", true, "将日志输出到文件")
	flag.StringVar(&logFilePath, "log-file", "", "日志文件路径（默认为当前目录下的时间戳文件）")
	flag.BoolVar(&safeMode, "safe", false, "安全模式：启用所有防卡住修复，推荐用于大批量或不稳定环境")
	flag.BoolVar(&aggressiveMode, "aggressive", false, "激进模式：使用最短超时(10秒)，快速跳过卡住的剧集，适用于API不稳定时")
	flag.BoolVar(&monitorTest, "test-monitoring", false, "测试监控系统")

	// 同步相关参数
	flag.Uint64Var(&showID, "show-id", 0, "剧集ID（用于单个同步）")
	flag.StringVar(&showIDs, "show-ids", "", "剧集ID列表，逗号分隔（用于批量同步）")
	flag.BoolVar(&syncAll, "all", true, "同步所有未同步的剧集")
	flag.IntVar(&limitBatch, "limit-batch", 0, "限制批量同步的最大数量（0表示全部）")
	flag.IntVar(&pageSize, "page-size", 50, "每批处理的剧集数量（默认50，最大200）")
	flag.Uint64Var(&startID, "start-id", 0, "同步的起始剧集ID（包含）")
	flag.Uint64Var(&endID, "end-id", 0, "同步的结束剧集ID（包含）")
	flag.Uint64Var(&lastID, "last-id", 0, "[已弃用，请使用 --start-id] 从指定的show ID开始同步（大于此ID的剧集）")
	flag.BoolVar(&skipExisting, "skip-existing", true, "跳过已有记录的剧集")
	flag.BoolVar(&autoConfirm, "yes", false, "自动确认操作，跳过交互确认")

	// 查询相关参数
	flag.StringVar(&imdbID, "imdb-id", "", "IMDB ID（用于搜索）")
	flag.Uint64Var(&tmdbID, "tmdb-id", 0, "TMDB ID（用于搜索）")

	// 分页参数
	flag.IntVar(&limit, "limit", 100, "查询限制")
	flag.IntVar(&offset, "offset", 0, "查询偏移量")
}

// initLogger 初始化日志系统
func initLogger() {
	if !logToFile {
		// 如果不输出到文件，使用标准输出
		logger = log.New(os.Stdout, "", log.LstdFlags|log.Lmicroseconds)
		return
	}

	// 生成日志文件名
	if logFilePath == "" {
		timestamp := time.Now().Format("20060102_150405")
		logFilePath = fmt.Sprintf("external_ids_sync_%s.log", timestamp)
	}

	var err error
	logFile, err = os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Fatal("无法创建日志文件:", err)
	}

	logger = log.New(logFile, "", log.LstdFlags|log.Lmicroseconds)

	// 在终端显示日志文件路径
	fmt.Printf("日志文件: %s\n", logFilePath)
	fmt.Println("使用 tail -f " + logFilePath + " 查看实时日志")
	fmt.Println("========================================")

	// 写入日志头
	logger.Println("========================================")
	logger.Println("外部ID同步工具日志")
	logger.Printf("启动时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	logger.Println("========================================")
}

// debugLog 调试日志（只在debug或verbose模式下输出）
func debugLog(format string, args ...interface{}) {
	if debug || verbose {
		// 快速模式下跳过大部分调试日志
		if fastMode && !strings.Contains(format, "ERROR") && !strings.Contains(format, "WARN") {
			return
		}

		if logger != nil {
			logger.Printf("[DEBUG] "+format, args...)
			smartLogSync()
		} else {
			fmt.Printf("[DEBUG] "+format+"\n", args...)
		}
	}
}

// infoLog 信息日志（总是输出到日志，重要信息也输出到终端）
func infoLog(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	if logger != nil {
		logger.Printf("[INFO] " + msg)
		smartLogSync()
	}
	// 快速模式下减少终端输出
	if !fastMode || strings.Contains(msg, "完成") || strings.Contains(msg, "开始") {
		fmt.Print(msg)
		if !strings.HasSuffix(msg, "\n") {
			fmt.Println()
		}
	}
}

// errorLog 错误日志
func errorLog(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	if logger != nil {
		logger.Printf("[ERROR] " + msg)
		// 错误日志强制立即同步
		if logFile != nil {
			logFile.Sync()
		}
	}
	// 错误信息总是输出到终端
	fmt.Printf("[ERROR] " + msg)
	if !strings.HasSuffix(msg, "\n") {
		fmt.Println()
	}
}

// progressLog 进度日志（只输出到终端）
func progressLog(format string, args ...interface{}) {
	// 快速模式下减少进度输出频率
	if fastMode && !strings.Contains(format, "完成") && !strings.Contains(format, "总进度") {
		return
	}

	fmt.Printf(format, args...)
	if !strings.HasSuffix(format, "\n") {
		fmt.Println()
	}
	// 记录到日志文件
	if logger != nil {
		logger.Printf("[PROGRESS] "+format, args...)
		smartLogSync()
	}
}

// outputLog 通用输出日志（记录到文件，可选输出到终端）
func outputLog(toTerminal bool, format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	if logger != nil {
		logger.Printf("[OUTPUT] " + msg)
		smartLogSync()
	}
	if toTerminal && (!fastMode || strings.Contains(msg, "ERROR") || strings.Contains(msg, "完成")) {
		fmt.Print(msg)
		if !strings.HasSuffix(msg, "\n") {
			fmt.Println()
		}
	}
}

// ========================================
// 监控和采样系统模块
// ========================================

// initMonitoringSystem 初始化监控系统
func initMonitoringSystem() error {
	config := monitor.DefaultCmdMonitorConfig()

	// 根据命令行参数调整配置
	config.AppName = "external_ids_sync"
	config.Version = "1.0.0"
	config.Environment = getEnvironment()

	// 🎯 根据模式调整采样策略
	if fastMode {
		// 快速模式：降低采样率，减少开销
		config.SamplingRate = 0.1 // 10%采样率
		config.MaxSamplesPerSecond = 50
		config.MinSeverity = monitor.SeverityWarn
		config.EnableDetailedTiming = false
		config.SystemMetricsInterval = 60 * time.Second
		config.ProgressReportInterval = 30 * time.Second
		infoLog("🚀 [快速模式] 采样率: 10%, 最大50样本/秒, 只记录WARN+")
	} else if aggressiveMode {
		// 激进模式：重点监控错误和性能问题
		config.SamplingRate = 0.5 // 50%采样率
		config.MaxSamplesPerSecond = 20
		config.MinSeverity = monitor.SeverityError // 只记录错误及以上
		config.EnableDetailedTiming = true
		config.EnableStackTrace = true
		config.SystemMetricsInterval = 15 * time.Second
		infoLog("🔥 [激进模式] 采样率: 50%, 最大20样本/秒, 只记录ERROR+")
	} else if safeMode {
		// 安全模式：全面监控，但控制输出
		config.SamplingRate = 1.0 // 全采样
		config.MaxSamplesPerSecond = 100
		config.MinSeverity = monitor.SeverityInfo
		config.EnableDetailedTiming = true
		config.EnableStackTrace = true
		config.EnableBufferOutput = true // 启用缓冲，以防日志丢失
		config.BufferMaxSize = 2000
		infoLog("🛡️  [安全模式] 采样率: 100%, 最大100样本/秒, 记录INFO+")
	} else if debug {
		// 调试模式：详细监控
		config.SamplingRate = 1.0
		config.MaxSamplesPerSecond = 200
		config.MinSeverity = monitor.SeverityDebug
		config.EnableDetailedTiming = true
		config.EnableStackTrace = true
		config.ConsoleColored = true
		infoLog("🐛 [调试模式] 采样率: 100%, 最大200样本/秒, 记录DEBUG+")
	} else {
		// 普通模式：平衡性能和监控
		config.SamplingRate = 0.3 // 30%采样率
		config.MaxSamplesPerSecond = 100
		config.MinSeverity = monitor.SeverityInfo
		infoLog("📊 [普通模式] 采样率: 30%, 最大100样本/秒, 记录INFO+")
	}

	// 设置输出配置
	config.EnableFileOutput = logToFile
	config.EnableConsoleOutput = !fastMode // 快速模式下减少控制台输出
	config.LogDirectory = "./monitoring"

	// 在测试模式下使用固定前缀，避免command为空的问题
	if monitorTest {
		config.LogFilePrefix = "external_ids_sync_test"
	} else {
		config.LogFilePrefix = fmt.Sprintf("external_ids_sync_%s", command)
	}

	var err error
	globalMonitor, err = monitor.NewCmdMonitor(config)
	if err != nil {
		return fmt.Errorf("failed to initialize monitoring: %w", err)
	}

	// 记录启动配置
	globalMonitor.RecordEvent("app.started", monitor.SeverityInfo, map[string]interface{}{
		"command":         command,
		"config_file":     configFile,
		"fast_mode":       fastMode,
		"safe_mode":       safeMode,
		"aggressive_mode": aggressiveMode,
		"debug_mode":      debug,
		"page_size":       pageSize,
		"limit_batch":     limitBatch,
		"sampling_config": config,
	})

	infoLog("✅ 监控系统已初始化，采样率: %.1f%%, 最大样本数: %d/秒",
		config.SamplingRate*100, config.MaxSamplesPerSecond)

	return nil
}

// getEnvironment 获取环境标识
func getEnvironment() string {
	if debug {
		return "debug"
	}
	if aggressiveMode {
		return "aggressive"
	}
	if safeMode {
		return "safe"
	}
	if fastMode {
		return "fast"
	}
	return "production"
}

// stopMonitoringSystem 停止监控系统
func stopMonitoringSystem() {
	if globalMonitor == nil {
		return
	}

	// 获取最终统计信息
	finalStats := globalMonitor.GetStats()

	// 输出监控报告
	if verbose || debug {
		infoLog("\n📊 ========== 监控报告 ==========")

		if stats, ok := finalStats["counters"].(map[string]int64); ok {
			infoLog("📈 计数器指标:")
			for name, value := range stats {
				infoLog("  %s: %d", name, value)
			}
		}

		if stats, ok := finalStats["gauges"].(map[string]float64); ok && len(stats) > 0 {
			infoLog("📊 仪表盘指标:")
			for name, value := range stats {
				infoLog("  %s: %.2f", name, value)
			}
		}

		if samplerStats, ok := finalStats["sampler_stats"].(monitor.SamplerStats); ok {
			infoLog("🎯 采样统计:")
			infoLog("  总采样数: %d", samplerStats.TotalSamples)
			infoLog("  接受采样数: %d", samplerStats.AcceptedSamples)
			infoLog("  丢弃采样数: %d", samplerStats.DroppedSamples)
			infoLog("  错误数: %d", samplerStats.ErrorCount)
			if samplerStats.TotalSamples > 0 {
				acceptRate := float64(samplerStats.AcceptedSamples) / float64(samplerStats.TotalSamples) * 100
				infoLog("  采样接受率: %.2f%%", acceptRate)
			}
		}

		if uptime, ok := finalStats["uptime"].(string); ok {
			infoLog("⏱️  运行时长: %s", uptime)
		}

		infoLog("========================================")
	}

	// 导出缓冲区数据（如果启用）
	if safeMode {
		timestamp := time.Now().Format("20060102_150405")
		dumpPath := fmt.Sprintf("./monitoring/buffer_dump_%s.json", timestamp)
		if err := globalMonitor.DumpBuffer(dumpPath); err == nil {
			infoLog("💾 缓冲区数据已导出到: %s", dumpPath)
		}
	}

	// 停止监控
	if err := globalMonitor.Stop(); err != nil {
		errorLog("停止监控时出错: %v", err)
	} else {
		infoLog("🏁 监控系统已安全停止")
	}
}

// ========================================
// 防卡住功能模块
// ========================================

// extractGinContext 尝试从操作名称中提取gin.Context
// 这是一个辅助函数，用于在超时时获取更多上下文信息
func extractGinContext(operationName string) (*gin.Context, bool) {
	// 在命令行工具中，我们可能没有直接的gin.Context
	// 这里返回false表示无法提取
	return nil, false
}

// recordDatabaseStats 记录数据库连接池统计信息
func recordDatabaseStats() {
	if globalMonitor == nil {
		return
	}

	// 获取数据库连接池状态
	db := dbs.GetDB()
	if db == nil {
		return
	}

	sqlDB, err := db.DB()
	if err != nil {
		return
	}

	// 记录连接池统计
	globalMonitor.RecordGauge("db.connections.open", float64(sqlDB.Stats().OpenConnections))
	globalMonitor.RecordGauge("db.connections.in_use", float64(sqlDB.Stats().InUse))
	globalMonitor.RecordGauge("db.connections.idle", float64(sqlDB.Stats().Idle))
	globalMonitor.RecordGauge("db.connections.max_open", float64(sqlDB.Stats().MaxOpenConnections))

	// 记录等待统计
	globalMonitor.RecordGauge("db.wait_count", float64(sqlDB.Stats().WaitCount))
	globalMonitor.RecordGauge("db.wait_duration", float64(sqlDB.Stats().WaitDuration))
}

// TimeoutWrapper 为函数调用添加超时包装
func TimeoutWrapper(name string, timeout time.Duration, fn func() error) error {
	debugLog("🕐 开始执行 %s (超时: %v)...", name, timeout)

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	resultChan := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				resultChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		resultChan <- fn()
	}()

	select {
	case err := <-resultChan:
		if err != nil {
			debugLog("❌ %s 执行失败: %v", name, err)
		} else {
			debugLog("✅ %s 执行成功", name)
		}
		return err
	case <-ctx.Done():
		errorLog("⏰ %s 执行超时 (%v)", name, timeout)

		// 🎯 增强监控：记录超时事件到监控系统
		if globalMonitor != nil {
			// 尝试从上下文中提取show_id（如果可用）
			var showID interface{} = "unknown"
			if ginCtx, ok := extractGinContext(name); ok {
				if showIDStr, exists := ginCtx.Get("show_id"); exists {
					showID = showIDStr
				}
			}

			globalMonitor.RecordEvent("timeout.detailed", monitor.SeverityError, map[string]interface{}{
				"operation": name,
				"timeout":   timeout.String(),
				"show_id":   showID,
				"timestamp": time.Now().Unix(),
			})

			// 记录超时计数器
			globalMonitor.RecordCounter("timeout.total", 1)
			globalMonitor.RecordCounter(fmt.Sprintf("timeout.%s", name), 1)

			// 🎯 记录数据库状态（如果超时发生在数据库操作中）
			if strings.Contains(name, "check_existing") || strings.Contains(name, "db") {
				recordDatabaseStats()
			}
		}

		// 打印超时时的诊断信息
		errorLog("超时诊断信息:")
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		errorLog("  内存使用: %.2f MB", float64(m.Alloc)/1024/1024)
		errorLog("  Goroutine数量: %d", runtime.NumGoroutine())

		// 🎯 新增：检查断路器状态
		if strings.Contains(name, "external_api_call") {
			errorLog("  🔌 建议检查外部API服务状态")
			errorLog("  🌐 检查网络连接到: http://118.196.31.23:5310")
			errorLog("  📊 检查断路器是否开启")
		}

		return fmt.Errorf("%s timeout after %v", name, timeout)
	}
}

// TimeoutWrapperWithContext 为函数调用添加可传播取消的超时包装
// 该包装器会创建一个带超时的 context，并将其传递给 fn。
// 当发生超时时，context 会被取消，从而尽快中断下游网络/数据库调用，避免遗留阻塞导致后续全超时。
func TimeoutWrapperWithContext(name string, timeout time.Duration, fn func(ctx context.Context) error) error {
	debugLog("🕐 开始执行 %s (超时: %v)...", name, timeout)

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	resultChan := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				resultChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		resultChan <- fn(ctx)
	}()

	select {
	case err := <-resultChan:
		if err != nil {
			debugLog("❌ %s 执行失败: %v", name, err)
		} else {
			debugLog("✅ %s 执行成功", name)
		}
		return err
	case <-ctx.Done():
		errorLog("⏰ %s 执行超时 (%v)", name, timeout)

		if globalMonitor != nil {
			var showID interface{} = "unknown"
			if ginCtx, ok := extractGinContext(name); ok {
				if showIDStr, exists := ginCtx.Get("show_id"); exists {
					showID = showIDStr
				}
			}

			globalMonitor.RecordEvent("timeout.detailed", monitor.SeverityError, map[string]interface{}{
				"operation": name,
				"timeout":   timeout.String(),
				"show_id":   showID,
				"timestamp": time.Now().Unix(),
			})

			globalMonitor.RecordCounter("timeout.total", 1)
			globalMonitor.RecordCounter(fmt.Sprintf("timeout.%s", name), 1)

			if strings.Contains(name, "check_existing") || strings.Contains(name, "db") {
				recordDatabaseStats()
			}
		}

		errorLog("超时诊断信息:")
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		errorLog("  内存使用: %.2f MB", float64(m.Alloc)/1024/1024)
		errorLog("  Goroutine数量: %d", runtime.NumGoroutine())

		if strings.Contains(name, "external_api_call") {
			errorLog("  🔌 建议检查外部API服务状态")
			errorLog("  🌐 检查网络连接到: http://118.196.31.23:5310")
			errorLog("  📊 检查断路器是否开启")
		}

		return fmt.Errorf("%s timeout after %v", name, timeout)
	}
}

// AddStuckDetection 添加卡住检测
func AddStuckDetection() {
	// 设置信号处理，当程序卡住时可以按Ctrl+C查看状态
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-c
		errorLog("\n🚨 收到中断信号，正在诊断...")

		// 打印Goroutine堆栈
		buf := make([]byte, 64*1024)
		n := runtime.Stack(buf, true)
		errorLog("Goroutine堆栈:\n%s", string(buf[:n]))

		// 打印内存信息
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		errorLog("内存使用: %.2f MB", float64(m.Alloc)/1024/1024)
		errorLog("Goroutine数量: %d", runtime.NumGoroutine())

		os.Exit(1)
	}()
}

// antiStuckSyncOne 防卡住版本的单个同步（集成监控）
func antiStuckSyncOne(ctx *gin.Context, service *showService.ExternalIDsService, showID uint64) error {
	infoLog("🚀 [防卡住] 开始同步剧集 %d...", showID)

	// 🎯 使用监控系统跟踪操作
	var opTracker *monitor.OperationTracker
	if globalMonitor != nil {
		opTracker = globalMonitor.NewOperationTracker("sync_single_item", map[string]string{
			"show_id": fmt.Sprintf("%d", showID),
		}, map[string]interface{}{
			"show_id": showID,
		})

		// 记录开始事件
		globalMonitor.RecordEvent("sync_single.started", monitor.SeverityInfo, map[string]interface{}{
			"show_id": showID,
		})
	}

	// 1. 添加整体超时控制（5分钟）
	totalErr := TimeoutWrapper(fmt.Sprintf("sync_show_%d", showID), 5*time.Minute, func() error {

		// 2. 分步骤执行，每步都有超时和进度报告
		infoLog("📝 [步骤1/4] 检查现有记录...")

		// 🎯 监控检查现有记录的步骤
		var checkErr error
		if globalMonitor != nil {
			checkErr = globalMonitor.TimeFunc("sync_single.check_existing", map[string]string{
				"show_id": fmt.Sprintf("%d", showID),
			}, func() error {
				return TimeoutWrapperWithContext("check_existing", 120*time.Second, func(timeoutCtx context.Context) error {
					if !forceUpdate {
						// 创建新的gin.Context用于数据库查询，挂接包装器的超时context
						dbGinCtx := helper.GenGinCtx()
						dbGinCtx.Request = dbGinCtx.Request.WithContext(timeoutCtx)

						existing, err := service.GetExternalIDsByShowID(dbGinCtx, showID)
						if err == nil && existing != nil && existing.IsMatch == 2 {
							infoLog("✅ 剧集 %d 已有外部ID映射，使用 --force 强制更新", showID)
							return fmt.Errorf("already_exists")
						}
					}
					return nil
				})
			})
		} else {
			checkErr = TimeoutWrapperWithContext("check_existing", 120*time.Second, func(timeoutCtx context.Context) error {
				if !forceUpdate {
					// 创建新的gin.Context用于数据库查询，挂接包装器的超时context
					dbGinCtx := helper.GenGinCtx()
					dbGinCtx.Request = dbGinCtx.Request.WithContext(timeoutCtx)

					existing, err := service.GetExternalIDsByShowID(dbGinCtx, showID)
					if err == nil && existing != nil && existing.IsMatch == 2 {
						infoLog("✅ 剧集 %d 已有外部ID映射，使用 --force 强制更新", showID)
						return fmt.Errorf("already_exists")
					}
				}
				return nil
			})
		}

		if checkErr != nil && checkErr.Error() == "already_exists" {
			if globalMonitor != nil {
				globalMonitor.RecordEvent("sync_single.skipped_existing", monitor.SeverityInfo, map[string]interface{}{
					"show_id": showID,
				})
				globalMonitor.RecordCounter("sync_single.skipped_count", 1)
			}
			return nil // 正常退出
		} else if checkErr != nil {
			// 🎯 增强监控：记录数据库查询失败详情
			if globalMonitor != nil {
				globalMonitor.RecordError("sync_single.check_failed", checkErr, map[string]interface{}{
					"show_id": showID,
				})

				// 记录数据库查询失败类型
				if strings.Contains(checkErr.Error(), "timeout") {
					globalMonitor.RecordCounter("sync_single.db_timeout_errors", 1)
				} else if strings.Contains(checkErr.Error(), "connection") {
					globalMonitor.RecordCounter("sync_single.db_connection_errors", 1)
				} else {
					globalMonitor.RecordCounter("sync_single.db_other_errors", 1)
				}

				// 记录数据库查询失败事件
				globalMonitor.RecordEvent("sync_single.db_query_failed", monitor.SeverityError, map[string]interface{}{
					"show_id":    showID,
					"error_type": "check_existing",
					"error_msg":  checkErr.Error(),
					"timestamp":  time.Now().Unix(),
				})
			}
			return fmt.Errorf("检查现有记录失败: %w", checkErr)
		}

		infoLog("📡 [步骤2/4] 调用外部API...")
		startTime := time.Now()

		// 3. 关键的API调用，添加超时和重试机制
		var apiErr error
		if globalMonitor != nil {
			apiErr = globalMonitor.TimeFunc("sync_single.api_call", map[string]string{
				"show_id":      fmt.Sprintf("%d", showID),
				"api_provider": "external_ids",
			}, func() error {
				return TimeoutWrapperWithContext("external_api_call", 45*time.Second, func(timeoutCtx context.Context) error {
					apiGinCtx := helper.GenGinCtx()
					apiGinCtx.Request = apiGinCtx.Request.WithContext(timeoutCtx)
					return service.SyncExternalIDs(apiGinCtx, showID)
				})
			})
		} else {
			apiErr = TimeoutWrapperWithContext("external_api_call", 45*time.Second, func(timeoutCtx context.Context) error {
				apiGinCtx := helper.GenGinCtx()
				apiGinCtx.Request = apiGinCtx.Request.WithContext(timeoutCtx)
				return service.SyncExternalIDs(apiGinCtx, showID)
			})
		}

		if apiErr != nil {
			// 如果超时，尝试短超时重试一次
			infoLog("⚠️  第一次尝试失败，使用短超时重试...")

			// 🎯 增强监控：记录重试事件和错误详情
			if globalMonitor != nil {
				// 记录重试事件
				globalMonitor.RecordEvent("sync_single.api_retry", monitor.SeverityWarn, map[string]interface{}{
					"show_id":     showID,
					"first_error": apiErr.Error(),
					"retry_count": 1,
					"timestamp":   time.Now().Unix(),
				})

				// 记录重试计数器
				globalMonitor.RecordCounter("sync_single.api_retries", 1)

				// 记录错误类型统计
				if strings.Contains(apiErr.Error(), "timeout") {
					globalMonitor.RecordCounter("sync_single.timeout_errors", 1)
				} else if strings.Contains(apiErr.Error(), "context deadline exceeded") {
					globalMonitor.RecordCounter("sync_single.context_deadline_errors", 1)
				} else {
					globalMonitor.RecordCounter("sync_single.other_errors", 1)
				}
			}

			if globalMonitor != nil {
				apiErr = globalMonitor.TimeFunc("sync_single.api_call_retry", map[string]string{
					"show_id": fmt.Sprintf("%d", showID),
					"retry":   "true",
				}, func() error {
					return TimeoutWrapperWithContext("external_api_call_retry", 60*time.Second, func(timeoutCtx context.Context) error {
						apiGinCtx := helper.GenGinCtx()
						apiGinCtx.Request = apiGinCtx.Request.WithContext(timeoutCtx)
						return service.SyncExternalIDs(apiGinCtx, showID)
					})
				})
			} else {
				apiErr = TimeoutWrapperWithContext("external_api_call_retry", 60*time.Second, func(timeoutCtx context.Context) error {
					apiGinCtx := helper.GenGinCtx()
					apiGinCtx.Request = apiGinCtx.Request.WithContext(timeoutCtx)
					return service.SyncExternalIDs(apiGinCtx, showID)
				})
			}
		}

		if apiErr != nil {
			if globalMonitor != nil {
				globalMonitor.RecordError("sync_single.api_failed", apiErr, map[string]interface{}{
					"show_id": showID,
				})
				globalMonitor.RecordCounter("sync_single.api_errors", 1)
			}
			return fmt.Errorf("API调用失败: %w", apiErr)
		}

		elapsed := time.Since(startTime)
		infoLog("⚡ API调用完成，耗时: %.2f秒", elapsed.Seconds())

		if globalMonitor != nil {
			globalMonitor.RecordCounter("sync_single.api_success", 1)
			globalMonitor.RecordGauge("sync_single.api_duration_seconds", elapsed.Seconds())
		}

		infoLog("📊 [步骤3/4] 获取同步结果...")

		// 🎯 监控获取结果的步骤
		var resultErr error
		if globalMonitor != nil {
			resultErr = globalMonitor.TimeFunc("sync_single.get_result", map[string]string{
				"show_id": fmt.Sprintf("%d", showID),
			}, func() error {
				return TimeoutWrapperWithContext("get_result", 30*time.Second, func(timeoutCtx context.Context) error {
					dbGinCtx := helper.GenGinCtx()
					dbGinCtx.Request = dbGinCtx.Request.WithContext(timeoutCtx)
					result, err := service.GetExternalIDsByShowID(dbGinCtx, showID)
					if err != nil {
						return err
					}

					if result != nil {
						infoLog("📋 [步骤4/4] 同步结果:")
						printExternalIDs(result)
						if globalMonitor != nil {
							globalMonitor.RecordEvent("sync_single.result_found", monitor.SeverityInfo, map[string]interface{}{
								"show_id":  showID,
								"has_imdb": result.ImdbID != "",
								"has_tmdb": result.TmdbID != nil && *result.TmdbID > 0,
								"is_match": result.IsMatch,
							})
						}
					} else {
						infoLog("❌ 未找到匹配的外部ID")
						if globalMonitor != nil {
							globalMonitor.RecordEvent("sync_single.no_result", monitor.SeverityWarn, map[string]interface{}{
								"show_id": showID,
							})
						}
					}

					return nil
				})
			})
		} else {
			resultErr = TimeoutWrapperWithContext("get_result", 30*time.Second, func(timeoutCtx context.Context) error {
				dbGinCtx := helper.GenGinCtx()
				dbGinCtx.Request = dbGinCtx.Request.WithContext(timeoutCtx)
				result, err := service.GetExternalIDsByShowID(dbGinCtx, showID)
				if err != nil {
					return err
				}

				if result != nil {
					infoLog("📋 [步骤4/4] 同步结果:")
					printExternalIDs(result)
				} else {
					infoLog("❌ 未找到匹配的外部ID")
				}

				return nil
			})
		}

		if resultErr != nil {
			if globalMonitor != nil {
				globalMonitor.RecordError("sync_single.get_result_failed", resultErr, map[string]interface{}{
					"show_id": showID,
				})
			}
			return fmt.Errorf("获取结果失败: %w", resultErr)
		}

		totalElapsed := time.Since(startTime)
		infoLog("✅ 剧集 %d 同步完成！总耗时: %.2f秒", showID, totalElapsed.Seconds())

		if globalMonitor != nil {
			globalMonitor.RecordGauge("sync_single.total_duration_seconds", totalElapsed.Seconds())
			globalMonitor.RecordEvent("sync_single.completed", monitor.SeverityInfo, map[string]interface{}{
				"show_id":        showID,
				"total_duration": totalElapsed.String(),
			})
		}

		return nil
	})

	// 🎯 记录操作结果
	if globalMonitor != nil {
		if totalErr != nil {
			opTracker.Error(totalErr)
			globalMonitor.RecordCounter("sync_single.total_errors", 1)
		} else {
			opTracker.Success()
			globalMonitor.RecordCounter("sync_single.total_success", 1)
		}
	}

	return totalErr
}

// antiStuckSyncBatch 防卡住版本的批量同步（集成监控）
func antiStuckSyncBatch(ctx *gin.Context, service *showService.ExternalIDsService, ids []uint64) map[uint64]error {
	infoLog("🚀 [防卡住批量] 开始同步 %d 个剧集...", len(ids))

	// 🎯 创建进度跟踪器
	var progressTracker *monitor.ProgressTracker
	if globalMonitor != nil {
		progressTracker = globalMonitor.NewProgressTracker("sync_batch_progress", int64(len(ids)), map[string]string{
			"operation": "external_ids_sync",
		})
		defer progressTracker.Complete()

		// 记录批量同步开始
		globalMonitor.RecordEvent("sync_batch.started", monitor.SeverityInfo, map[string]interface{}{
			"total_items": len(ids),
			"batch_size":  len(ids),
		})
	}

	results := make(map[uint64]error)
	successCount := 0
	errorCount := 0
	startTime := time.Now()

	// 逐个处理，每个都有独立的超时控制
	for i, showID := range ids {
		progressLog("📍 [%d/%d] 正在处理剧集 %d...", i+1, len(ids), showID)

		err := antiStuckSyncOne(ctx, service, showID)
		if err != nil {
			results[showID] = err
			errorCount++
			errorLog("❌ 剧集 %d 处理失败: %v", showID, err)
		} else {
			successCount++
			infoLog("✅ 剧集 %d 处理成功", showID)
		}

		// 🎯 更新进度跟踪器
		if progressTracker != nil {
			progressTracker.Update(int64(i + 1))
		}

		// 🎯 记录处理速率
		if globalMonitor != nil {
			elapsed := time.Since(startTime)
			if elapsed.Seconds() > 0 {
				rate := float64(i+1) / elapsed.Seconds()
				globalMonitor.RecordGauge("sync_batch.processing_rate", rate)
			}
		}

		// 显示当前统计
		if (i+1)%10 == 0 || i == len(ids)-1 {
			progressLog("📊 当前统计: 成功 %d, 失败 %d, 进度 %.1f%%",
				successCount, errorCount, float64(i+1)/float64(len(ids))*100)

			// 🎯 记录中间统计
			if globalMonitor != nil {
				globalMonitor.RecordEvent("sync_batch.progress_report", monitor.SeverityInfo, map[string]interface{}{
					"processed":     i + 1,
					"total":         len(ids),
					"success_count": successCount,
					"error_count":   errorCount,
					"success_rate":  float64(successCount) / float64(i+1) * 100,
				})
			}
		}

		// 检查是否应该继续
		if i < len(ids)-1 {
			debugLog("⏳ 等待2秒后继续下一个...")
			time.Sleep(2 * time.Second)

			// 检查内存使用
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			memUsageMB := float64(m.Alloc) / 1024 / 1024

			// 🎯 记录内存使用
			if globalMonitor != nil {
				globalMonitor.RecordGauge("sync_batch.memory_usage_mb", memUsageMB)
			}

			if memUsageMB > 500 { // 超过500MB
				infoLog("⚠️  内存使用较高(%.1fMB)，强制GC...", memUsageMB)
				runtime.GC()

				if globalMonitor != nil {
					globalMonitor.RecordEvent("sync_batch.gc_triggered", monitor.SeverityWarn, map[string]interface{}{
						"memory_mb": memUsageMB,
						"processed": i + 1,
					})
				}
			}
		}
	}

	// 统计结果
	totalDuration := time.Since(startTime)
	infoLog("\n🏁 批量同步完成！")
	infoLog("✅ 成功: %d个", successCount)
	infoLog("❌ 失败: %d个", errorCount)
	infoLog("📊 成功率: %.1f%%", float64(successCount)/float64(len(ids))*100)
	infoLog("⏱️  总耗时: %s", totalDuration.String())
	if len(ids) > 0 {
		infoLog("⚡ 平均: %.2f秒/个", totalDuration.Seconds()/float64(len(ids)))
	}

	// 🎯 记录最终统计
	if globalMonitor != nil {
		globalMonitor.RecordEvent("sync_batch.completed", monitor.SeverityInfo, map[string]interface{}{
			"total_processed": len(ids),
			"success_count":   successCount,
			"error_count":     errorCount,
			"total_duration":  totalDuration.String(),
			"avg_per_item":    totalDuration.Seconds() / float64(len(ids)),
			"success_rate":    float64(successCount) / float64(len(ids)) * 100,
		})

		// 记录性能指标
		globalMonitor.RecordGauge("sync_batch.final_success_rate", float64(successCount)/float64(len(ids))*100)
		globalMonitor.RecordCounter("sync_batch.total_processed", int64(len(ids)))
		globalMonitor.RecordCounter("sync_batch.total_success", int64(successCount))
		globalMonitor.RecordCounter("sync_batch.total_errors", int64(errorCount))
		globalMonitor.RecordGauge("sync_batch.total_duration_seconds", totalDuration.Seconds())
	}

	return results
}

// antiStuckSyncBatchWrapper 防卡住批量同步包装函数
func antiStuckSyncBatchWrapper(ctx *gin.Context, service *showService.ExternalIDsService) {
	// 🎯 初始化资源监控器
	InitResourceMonitor(service)
	if resourceMonitor := GetResourceMonitor(); resourceMonitor != nil {
		resourceMonitor.Start()
		defer resourceMonitor.Stop()
		infoLog("📊 资源监控器已启动")

		// 检查初始资源状态
		if resourceMonitor.CheckResourceLimits() {
			errorLog("⚠️  系统资源接近限制，建议稍后重试")
		}
	}

	// 🎯 初始化进程协调器
	InitProcessCoordinator()
	if coordinator := GetProcessCoordinator(); coordinator != nil {
		taskRange := fmt.Sprintf("batch-%d", time.Now().Unix())
		numWorkers := 3 // 默认worker数量

		if err := coordinator.Start(taskRange, numWorkers); err != nil {
			errorLog("启动进程协调器失败: %v", err)
		} else {
			defer coordinator.Stop()
			infoLog("🤝 进程协调器已启动")

			// 检查资源冲突并调整配置
			if report, err := coordinator.CheckResourceConflicts(); err == nil {
				if len(report.Warnings) > 0 {
					for _, warning := range report.Warnings {
						errorLog("⚠️  %s", warning)
					}

					// 建议最优配置
					if config := coordinator.SuggestOptimalConfig(); config != nil {
						infoLog("💡 建议配置: 最大进程数=%d, 每进程worker数=%d",
							config.MaxConcurrentProcesses, config.MaxWorkersPerProcess)

						// 如果检测到多进程，减少worker数量
						if report.TotalProcesses > 1 {
							numWorkers = 2 // 多进程时减少worker数量
							infoLog("🔧 检测到多进程运行，已调整worker数量为%d", numWorkers)
						}
					}
				}
			}
		}
	}

	// 复用原来的 syncBatch 函数的 ID 获取逻辑
	debugLog("进入antiStuckSyncBatchWrapper函数:")
	debugLog("  - syncAll = %v", syncAll)
	debugLog("  - showIDs = %s", showIDs)
	debugLog("  - limitBatch = %d", limitBatch)
	debugLog("  - pageSize = %d", pageSize)

	// 验证pageSize参数
	if pageSize <= 0 {
		pageSize = 100
	} else if pageSize > 200 {
		pageSize = 200
		infoLog("每批处理数量超过最大限制，调整为200")
	}

	var ids []uint64

	// 判断是同步所有还是指定ID
	if syncAll {
		// 处理参数兼容性：如果使用了旧的 lastID 参数，转换为 startID
		if lastID > 0 && startID == 0 {
			infoLog("[警告] --last-id 参数已弃用，请使用 --start-id")
			startID = lastID + 1 // lastID 是"大于"的语义，所以要+1
		}

		// 验证 ID 范围参数
		if startID > 0 && endID > 0 && startID > endID {
			errorLog("错误：起始ID(%d)不能大于结束ID(%d)", startID, endID)
			log.Fatalf("错误：起始ID(%d)不能大于结束ID(%d)", startID, endID)
		}

		// 显示同步范围信息
		if startID > 0 && endID > 0 {
			infoLog("同步范围: show_id %d 到 %d", startID, endID)
		} else if startID > 0 {
			infoLog("同步范围: show_id >= %d", startID)
		} else if endID > 0 {
			infoLog("同步范围: show_id <= %d", endID)
		}

		// 获取未同步总数
		infoLog("正在统计未同步的剧集数量...")
		var totalUnsynced int64
		var err error

		if startID > 0 || endID > 0 {
			totalUnsynced, err = service.GetUnsyncedCountInRange(ctx, startID, endID)
		} else {
			totalUnsynced, err = service.GetUnsyncedCount(ctx)
		}

		if err != nil {
			errorLog("获取未同步总数失败: %v", err)
			log.Fatalf("获取未同步总数失败: %v", err)
		}

		if totalUnsynced == 0 {
			infoLog("所有剧集都已同步！")
			return
		}

		infoLog("总计有 %d 个剧集未同步", totalUnsynced)

		// 确定要处理的数量
		processCount := int(totalUnsynced)
		if limitBatch > 0 && limitBatch < processCount {
			processCount = limitBatch
			infoLog("根据--limit-batch参数，将处理前 %d 个剧集", processCount)
		}

		// 如果数量较多，需要用户确认
		if !autoConfirm && processCount > 100 {
			outputLog(true, "将要同步 %d 个剧集，是否继续？(y/N): ", processCount)
			var response string
			fmt.Scanln(&response)
			if strings.ToLower(response) != "y" {
				infoLog("操作已取消")
				return
			}
		}

		// 分页获取并处理
		infoLog("开始分页获取未同步的剧集ID...")
		infoLog("配置: 每页%d个，总计处理%d个", pageSize, processCount)

		offset := 0
		for len(ids) < processCount {
			// 计算本批次需要获取的数量
			remaining := processCount - len(ids)
			batchLimit := pageSize
			if batchLimit > remaining {
				batchLimit = remaining
			}

			debugLog("获取第%d页，offset=%d, limit=%d", (offset/pageSize)+1, offset, batchLimit)

			// 分页获取
			var pageIDs []uint64
			if startID > 0 || endID > 0 {
				pageIDs, err = service.GetUnsyncedShowIDsPaginatedInRange(ctx, startID, endID, offset, batchLimit)
			} else {
				pageIDs, err = service.GetUnsyncedShowIDsPaginated(ctx, offset, batchLimit)
			}

			if err != nil {
				errorLog("获取未同步剧集失败: %v", err)
				log.Fatalf("获取未同步剧集失败: %v", err)
			}

			if len(pageIDs) == 0 {
				// 没有更多数据
				debugLog("没有更多未同步的剧集")
				break
			}

			ids = append(ids, pageIDs...)
			offset += pageSize

			progressLog("已获取 %d/%d 个剧集ID", len(ids), processCount)
		}

		if len(ids) == 0 {
			infoLog("没有找到未同步的剧集")
			return
		}

		infoLog("共获取到 %d 个未同步的剧集ID", len(ids))
	} else {
		// 使用指定的ID列表
		if showIDs == "" {
			log.Fatal("请指定 --show-ids 参数（逗号分隔的ID列表）或使用 --all 同步所有")
		}

		// 解析ID列表
		idStrings := strings.Split(showIDs, ",")
		for _, idStr := range idStrings {
			idStr = strings.TrimSpace(idStr)
			if idStr == "" {
				continue
			}
			id, err := strconv.ParseUint(idStr, 10, 64)
			if err != nil {
				log.Fatalf("无效的ID: %s", idStr)
			}
			ids = append(ids, id)
		}

		if len(ids) == 0 {
			log.Fatal("没有有效的ID")
		}
	}

	// 🚀 使用防卡住版本执行批量同步
	infoLog("🛡️ 启动防卡住批量同步模式，共%d个剧集", len(ids))
	results := antiStuckSyncBatch(ctx, service, ids)

	// 显示失败详情
	if len(results) > 0 && verbose {
		outputLog(true, "\n同步失败的剧集:")
		failCount := 0
		for showID, err := range results {
			if err != nil {
				outputLog(true, "剧集 %d: %v", showID, err)
				failCount++
				if failCount >= 10 && !debug {
					outputLog(true, "... 更多错误省略（使用 --debug 查看全部）")
					break
				}
			}
		}
	}
}

// ========================================
// 防卡住功能模块结束
// ========================================

func main() {
	fmt.Println("========================================")
	fmt.Println("外部ID同步工具启动中...")
	fmt.Println("========================================")

	flag.Parse()

	// 初始化日志系统
	initLogger()
	defer func() {
		if logFile != nil {
			logger.Println("========================================")
			logger.Println("程序执行完成")
			logger.Printf("结束时间: %s", time.Now().Format("2006-01-02 15:04:05"))
			logger.Printf("总日志条数: %d", logCounter)
			logger.Println("========================================")
			// 程序结束时强制同步所有剩余日志
			logFile.Sync()
			logFile.Close()
			fmt.Printf("\n📝 日志已保存到: %s (共%d条)\n", logFilePath, logCounter)
		}
	}()

	// 🎯 初始化监控和采样系统
	infoLog("初始化监控和采样系统...")
	if err := initMonitoringSystem(); err != nil {
		errorLog("初始化监控系统失败: %v", err)
		log.Fatalf("初始化监控系统失败: %v", err)
	}
	defer stopMonitoringSystem()

	// 添加调试输出
	debugLog("命令行参数解析结果:")
	debugLog("  - command: %s", command)
	debugLog("  - syncAll: %v", syncAll)
	debugLog("  - showIDs: %s", showIDs)
	debugLog("  - limitBatch: %d", limitBatch)
	debugLog("  - configFile: %s", configFile)
	debugLog("  - debug: %v", debug)
	debugLog("  - verbose: %v", verbose)
	debugLog("  - 原始参数: %v", os.Args)

	// 测试监控系统（优先于命令检查）
	if monitorTest {
		infoLog("🧪 测试监控系统...")
		testMonitoring()
		infoLog("✅ 监控系统测试完成")
		os.Exit(0)
	}

	// 检查命令
	if command == "" {
		printUsage()
		os.Exit(1)
	}

	infoLog("执行命令: %s", command)

	// 初始化配置
	infoLog("加载配置文件: %s", configFile)
	loadConfig(configFile)
	debugLog("配置文件加载完成")

	// 初始化数据库
	infoLog("初始化数据库连接...")
	initDatabase()
	debugLog("数据库连接初始化完成")

	// 创建服务实例（传递debug标志和模式信息）
	// TODO: 未来可以传递logger到service
	externalIDsService := showService.NewExternalIDsServiceWithDebug(debug)

	// 根据模式调整服务配置
	if aggressiveMode {
		// 激进模式：设置更短的超时时间
		infoLog("🔥 [激进模式] 启用最短超时策略，快速跳过卡住的剧集")
	}

	// 创建统一的gin.Context，用于整个命令行工具
	// 使用helper.GenGinCtx()确保context包含完整的请求信息和requestID
	ctx := helper.GenGinCtx()

	// 如果需要重置断路器
	if resetCircuitBrk {
		fmt.Println("[信息] 正在重置断路器状态...")
		externalIDsService.ResetCircuitBreaker()
		fmt.Println("[信息] ✅ 断路器已重置，可以继续正常处理")
		fmt.Println()
	}

	// 显示模式提示
	if aggressiveMode {
		fmt.Println("🔥 [激进模式已启用] 使用最短超时，快速跳过卡住的剧集")
		fmt.Println("   ⚡ API超时: 10秒（默认15秒）")
		fmt.Println("   ⚡ 任务超时: 20秒（默认60秒）")
		fmt.Println("   ⚡ 重试次数: 1次（默认2次）")
		fmt.Println("   🎯 适用场景: API服务不稳定，频繁卡住")
		fmt.Println("   ⚠️  注意: 可能增加失败率，但避免长时间等待")
		fmt.Println()
		// 激进模式自动启用安全模式
		if !safeMode {
			safeMode = true
			fmt.Println("   🔄 自动启用安全模式以配合激进策略")
		}
	} else if safeMode {
		fmt.Println("🛡️  [安全模式已启用] 启用所有防卡住修复机制")
		fmt.Println("   ✅ 强化HTTP超时控制")
		fmt.Println("   ✅ Worker任务级超时（20秒）")
		fmt.Println("   ✅ 全局操作超时监控")
		fmt.Println("   ✅ 非阻塞日志同步")
		fmt.Println("   ✅ 断路器监控优化")
		fmt.Printf("   📝 日志同步间隔: 每%d条（安全模式）\n", 200)
		fmt.Println()
		// 在安全模式下强制启用某些设置
		if !fastMode {
			fastMode = true
			fmt.Println("   🔄 自动启用快速模式以配合安全机制")
		}
	} else if fastMode {
		fmt.Println("🚀 [快速模式已启用] 减少日志输出，提升处理性能")
		fmt.Printf("📝 日志同步间隔: 每%d条同步一次磁盘\n", logSyncInterval)
		fmt.Println()
	} else if debug {
		fmt.Println("🐛 [调试模式已启用] 将显示详细的API请求和响应信息")
		fmt.Println("💡 提示: 使用 --fast、--safe 或 --aggressive 参数可大幅提升处理速度和稳定性")
		fmt.Println()
	}

	// ❗ 关键修复：为整个命令执行添加全局超时控制
	var globalTimeout time.Duration
	switch command {
	case "sync-one":
		globalTimeout = 5 * time.Minute // 单个同步5分钟超时
	case "sync-batch":
		globalTimeout = 4 * time.Hour // 批量同步4小时超时
	case "health-check":
		globalTimeout = 30 * time.Second // 健康检查30秒超时
	default:
		globalTimeout = 10 * time.Minute // 其他操作10分钟超时
	}

	// 创建带超时的context并关联到gin.Context
	globalCtx, globalCancel := context.WithTimeout(context.Background(), globalTimeout)
	defer globalCancel()

	// 将全局超时context关联到gin.Context
	ctx.Request = ctx.Request.WithContext(globalCtx)

	// 启动监控goroutine，定期检查是否超时
	go func() {
		<-globalCtx.Done()
		if globalCtx.Err() == context.DeadlineExceeded {
			errorLog("⚠️  操作超时！已运行%v，强制退出", globalTimeout)
			errorLog("💡 建议：")
			errorLog("  1. 使用 --page-size 减小批处理大小")
			errorLog("  2. 使用 --limit-batch 限制处理数量")
			errorLog("  3. 使用 --fast 模式提升性能")
			errorLog("  4. 检查网络连接和API服务状态")
			os.Exit(124) // 124 = timeout exit code
		}
	}()

	// 🛡️ 启用防卡住检测
	AddStuckDetection()
	infoLog("🛡️ 防卡住模式已启用，按 Ctrl+C 可查看诊断信息")

	// 执行命令
	switch command {
	case "sync-one":
		if showID == 0 {
			log.Fatal("请指定 --show-id 参数")
		}
		// 使用防卡住版本
		err := antiStuckSyncOne(ctx, externalIDsService, showID)
		if err != nil {
			errorLog("🚨 防卡住同步失败: %v", err)
			log.Fatalf("同步失败: %v", err)
		}
		infoLog("🎉 防卡住同步成功完成！")

	case "sync-batch":
		// 使用防卡住版本的批量同步
		antiStuckSyncBatchWrapper(ctx, externalIDsService)

	case "query":
		query(ctx, externalIDsService)
	case "stats":
		stats(ctx)
	case "search":
		search(ctx, externalIDsService)
	case "health-check":
		healthCheck(ctx, externalIDsService)
	default:
		errorLog("未知命令: %s", command)
		printUsage()
		os.Exit(1)
	}
}

// 同步单个剧集
func syncOne(ctx *gin.Context, service *showService.ExternalIDsService) {
	if showID == 0 {
		log.Fatal("请指定 --show-id 参数")
	}

	// 检查是否已存在
	if !forceUpdate {
		existing, err := service.GetExternalIDsByShowID(ctx, showID)
		if err == nil && existing != nil && existing.IsMatch == 2 {
			outputLog(true, "剧集 %d 已有外部ID映射，使用 --force 强制更新", showID)
			printExternalIDs(existing)
			return
		}
	}

	debugLog("开始同步剧集 ID: %d", showID)

	infoLog("正在同步剧集 %d 的外部ID...", showID)
	startTime := time.Now()

	err := service.SyncExternalIDs(ctx, showID)
	if err != nil {
		errorLog("同步失败: %v", err)
		log.Fatalf("同步失败: %v", err)
	}

	// 获取同步结果
	result, err := service.GetExternalIDsByShowID(ctx, showID)
	if err != nil {
		errorLog("获取同步结果失败: %v", err)
		log.Fatalf("获取同步结果失败: %v", err)
	}

	elapsed := time.Since(startTime)
	infoLog("✅ 同步成功！耗时: %.2f秒", elapsed.Seconds())

	if result != nil {
		printExternalIDs(result)
	} else {
		outputLog(true, "未找到匹配的外部ID")
	}
}

// 批量同步
func syncBatch(ctx *gin.Context, service *showService.ExternalIDsService) {
	// 添加调试输出
	debugLog("进入syncBatch函数:")
	debugLog("  - syncAll = %v", syncAll)
	debugLog("  - showIDs = %s", showIDs)
	debugLog("  - limitBatch = %d", limitBatch)
	debugLog("  - pageSize = %d", pageSize)
	debugLog("  - lastID = %d", lastID)

	// 验证pageSize参数
	if pageSize <= 0 {
		pageSize = 100
	} else if pageSize > 200 {
		pageSize = 200
		infoLog("每批处理数量超过最大限制，调整为200")
	}

	var ids []uint64

	// 判断是同步所有还是指定ID
	if syncAll {
		// 处理参数兼容性：如果使用了旧的 lastID 参数，转换为 startID
		if lastID > 0 && startID == 0 {
			infoLog("[警告] --last-id 参数已弃用，请使用 --start-id")
			startID = lastID + 1 // lastID 是"大于"的语义，所以要+1
		}

		// 验证 ID 范围参数
		if startID > 0 && endID > 0 && startID > endID {
			errorLog("错误：起始ID(%d)不能大于结束ID(%d)", startID, endID)
			log.Fatalf("错误：起始ID(%d)不能大于结束ID(%d)", startID, endID)
		}

		// 显示同步范围信息
		if startID > 0 && endID > 0 {
			infoLog("同步范围: show_id %d 到 %d", startID, endID)
		} else if startID > 0 {
			infoLog("同步范围: show_id >= %d", startID)
		} else if endID > 0 {
			infoLog("同步范围: show_id <= %d", endID)
		}

		// 获取未同步总数
		infoLog("正在统计未同步的剧集数量...")
		var totalUnsynced int64
		var err error

		if startID > 0 || endID > 0 {
			totalUnsynced, err = service.GetUnsyncedCountInRange(ctx, startID, endID)
		} else {
			totalUnsynced, err = service.GetUnsyncedCount(ctx)
		}

		if err != nil {
			errorLog("获取未同步总数失败: %v", err)
			log.Fatalf("获取未同步总数失败: %v", err)
		}

		if totalUnsynced == 0 {
			infoLog("所有剧集都已同步！")
			return
		}

		infoLog("总计有 %d 个剧集未同步", totalUnsynced)

		// 确定要处理的数量
		processCount := int(totalUnsynced)
		if limitBatch > 0 && limitBatch < processCount {
			processCount = limitBatch
			infoLog("根据--limit-batch参数，将处理前 %d 个剧集", processCount)
		}

		// 如果数量较多，需要用户确认
		if !autoConfirm && processCount > 100 {
			outputLog(true, "将要同步 %d 个剧集，是否继续？(y/N): ", processCount)
			var response string
			fmt.Scanln(&response)
			if strings.ToLower(response) != "y" {
				infoLog("操作已取消")
				return
			}
		}

		// 分页获取并处理
		infoLog("开始分页获取未同步的剧集ID...")
		infoLog("配置: 每页%d个，总计处理%d个", pageSize, processCount)

		offset := 0
		for len(ids) < processCount {
			// 计算本批次需要获取的数量
			remaining := processCount - len(ids)
			batchLimit := pageSize
			if batchLimit > remaining {
				batchLimit = remaining
			}

			debugLog("获取第%d页，offset=%d, limit=%d", (offset/pageSize)+1, offset, batchLimit)

			// 分页获取
			var pageIDs []uint64
			if startID > 0 || endID > 0 {
				pageIDs, err = service.GetUnsyncedShowIDsPaginatedInRange(ctx, startID, endID, offset, batchLimit)
			} else {
				pageIDs, err = service.GetUnsyncedShowIDsPaginated(ctx, offset, batchLimit)
			}

			if err != nil {
				errorLog("获取未同步剧集失败: %v", err)
				log.Fatalf("获取未同步剧集失败: %v", err)
			}

			if len(pageIDs) == 0 {
				// 没有更多数据
				debugLog("没有更多未同步的剧集")
				break
			}

			ids = append(ids, pageIDs...)
			offset += pageSize

			progressLog("已获取 %d/%d 个剧集ID", len(ids), processCount)
		}

		if len(ids) == 0 {
			infoLog("没有找到未同步的剧集")
			return
		}

		infoLog("共获取到 %d 个未同步的剧集ID", len(ids))
	} else {
		// 使用指定的ID列表
		if showIDs == "" {
			log.Fatal("请指定 --show-ids 参数（逗号分隔的ID列表）或使用 --all 同步所有")
		}

		// 解析ID列表
		idStrings := strings.Split(showIDs, ",")
		for _, idStr := range idStrings {
			idStr = strings.TrimSpace(idStr)
			if idStr == "" {
				continue
			}
			id, err := strconv.ParseUint(idStr, 10, 64)
			if err != nil {
				log.Fatalf("无效的ID: %s", idStr)
			}
			ids = append(ids, id)
		}

		if len(ids) == 0 {
			log.Fatal("没有有效的ID")
		}
	}

	infoLog("准备同步 %d 个剧集的外部ID...", len(ids))

	// 动态计算批处理大小
	apiSize := 20 // API调用每批大小
	if len(ids) < 100 {
		apiSize = 10 // 数量较少时，减小批次
	} else if len(ids) > 500 {
		apiSize = 30 // 数量较多时，增大批次
	}
	infoLog("性能优化配置：批量预加载genres，3个并发worker，每批%d个剧集，API间隔500ms", apiSize)

	startTime := time.Now()

	// 分批处理，避免一次性处理太多导致超时或内存问题
	batchSize := apiSize
	totalCount := len(ids)
	results := make(map[uint64]error)
	successTotal := 0
	failedTotal := 0

	for i := 0; i < totalCount; i += batchSize {
		end := i + batchSize
		if end > totalCount {
			end = totalCount
		}

		batch := ids[i:end]
		batchStartTime := time.Now()
		progressLog("[批次 %d/%d] 处理第 %d-%d 个剧集...",
			(i/batchSize)+1, (totalCount+batchSize-1)/batchSize, i+1, end)

		debugLog("批次 %d 包含的ShowIDs: %v", (i/batchSize)+1, batch)

		// 执行当前批次的同步
		infoLog("开始同步批次 %d，包含%d个剧集...", (i/batchSize)+1, len(batch))
		syncStartTime := time.Now()

		batchResults, err := service.BatchSyncExternalIDs(ctx, batch)

		syncDuration := time.Since(syncStartTime)
		debugLog("批次 %d 同步完成，耗时: %.2f秒", (i/batchSize)+1, syncDuration.Seconds())

		if err != nil {
			errorLog("批次同步出错: %v", err)
			// 记录整批失败
			for _, id := range batch {
				results[id] = err
			}
			failedTotal += len(batch)
			continue
		}

		// 合并结果
		successInBatch := len(batch) - len(batchResults)
		failedInBatch := len(batchResults)

		for k, v := range batchResults {
			results[k] = v
		}

		successTotal += successInBatch
		failedTotal += failedInBatch

		// 显示批次完成信息
		batchDuration := time.Since(batchStartTime)
		progressLog("[批次完成] 成功: %d/%d, 耗时: %v | 总进度: %d/%d (成功%d/失败%d)",
			successInBatch, len(batch), batchDuration,
			end, totalCount, successTotal, failedTotal)

		// 添加延迟，避免API限流（批次间延迟2秒）
		if end < totalCount {
			debugLog("等待2秒后继续下一批次...")
			time.Sleep(2 * time.Second)
		}
	}

	// 统计最终结果
	elapsed := time.Since(startTime)
	infoLog("========================================")
	infoLog("同步完成！")
	infoLog("总计: %d 个剧集", totalCount)
	infoLog("成功: %d 个", successTotal)
	infoLog("失败: %d 个", failedTotal)
	infoLog("耗时: %.2f秒", elapsed.Seconds())
	if totalCount > 0 {
		infoLog("平均: %.2f秒/个", elapsed.Seconds()/float64(totalCount))
	}
	infoLog("========================================")

	// 显示详细结果
	if verbose && successTotal > 0 {
		outputLog(true, "\n同步成功的剧集详情:")
		showCount := 0
		for _, id := range ids {
			if err, exists := results[id]; exists && err != nil {
				continue // 跳过失败的
			}
			result, err := service.GetExternalIDsByShowID(ctx, id)
			if err == nil && result != nil {
				outputLog(true, "\n剧集 %d:", id)
				printExternalIDs(result)
				showCount++
				if showCount >= 10 && !debug {
					outputLog(true, "\n... 更多结果省略（使用 --debug 查看全部）")
					break
				}
			}
		}
	}

	// 显示失败详情
	if failedTotal > 0 && verbose {
		outputLog(true, "\n同步失败的剧集:")
		failCount := 0
		for showID, err := range results {
			if err != nil {
				outputLog(true, "剧集 %d: %v", showID, err)
				failCount++
				if failCount >= 10 && !debug {
					outputLog(true, "... 更多错误省略（使用 --debug 查看全部）")
					break
				}
			}
		}
	}
}

// 查询外部ID
func query(ctx *gin.Context, service *showService.ExternalIDsService) {
	if showID > 0 {
		// 查询单个剧集
		result, err := service.GetExternalIDsByShowID(ctx, showID)
		if err != nil {
			errorLog("查询失败: %v", err)
			log.Fatalf("查询失败: %v", err)
		}
		if result == nil {
			outputLog(true, "剧集 %d 没有外部ID映射", showID)
			return
		}
		printExternalIDs(result)
	} else if showIDs != "" {
		// 批量查询
		idStrings := strings.Split(showIDs, ",")
		var ids []uint64
		for _, idStr := range idStrings {
			idStr = strings.TrimSpace(idStr)
			if idStr == "" {
				continue
			}
			id, err := strconv.ParseUint(idStr, 10, 64)
			if err != nil {
				log.Fatalf("无效的ID: %s", idStr)
			}
			ids = append(ids, id)
		}

		results, err := service.GetExternalIDsByShowIDs(ctx, ids)
		if err != nil {
			log.Fatalf("批量查询失败: %v", err)
		}

		if len(results) == 0 {
			outputLog(true, "没有找到任何外部ID映射")
			return
		}

		for _, result := range results {
			outputLog(true, "\n剧集 %d:", result.ShowID)
			printExternalIDs(result)
		}
	} else {
		// 查询所有（带分页）
		repo := externalIdsDao.GetRepo()
		// 使用 FindByFilter 替代 FindWithPagination
		results, err := repo.FindByFilter(ctx, &externalIdsDao.Filter{})
		if err != nil {
			log.Fatalf("查询失败: %v", err)
		}

		// 手动实现分页
		total := len(results)
		start := offset
		end := offset + limit
		if start > total {
			start = total
		}
		if end > total {
			end = total
		}
		pagedResults := results[start:end]

		outputLog(true, "共找到 %d 条记录（显示 %d-%d）:\n", total, start+1, end)
		for _, result := range pagedResults {
			printExternalIDs(result)
			outputLog(true, strings.Repeat("-", 50))
		}
	}
}

// 统计信息
func stats(ctx *gin.Context) {
	repo := externalIdsDao.GetRepo()

	// 获取所有记录进行统计
	allRecords, err := repo.FindByFilter(ctx, &externalIdsDao.Filter{})
	if err != nil {
		log.Fatalf("获取统计信息失败: %v", err)
	}

	// 手动计算统计信息
	totalCount := len(allRecords)
	matchedCount := 0
	noMatchCount := 0
	withIMDB := 0
	withTMDB := 0
	withTrakt := 0
	bySource := make(map[string]int)
	byMatchType := make(map[string]int)

	for _, record := range allRecords {
		if record.IsMatch == 2 {
			matchedCount++
		} else {
			noMatchCount++
		}

		if record.ImdbID != "" {
			withIMDB++
		}
		if record.TmdbID != nil && *record.TmdbID > 0 {
			withTMDB++
		}
		if record.TraktID != nil && *record.TraktID > 0 {
			withTrakt++
		}

		bySource[record.Source]++
		if record.MatchType != "" {
			byMatchType[record.MatchType]++
		}
	}

	outputLog(true, "=== 外部ID映射统计 ===")
	outputLog(true, "总记录数: %d", totalCount)
	outputLog(true, "有效匹配: %d", matchedCount)
	outputLog(true, "无匹配: %d", noMatchCount)

	if len(bySource) > 0 {
		outputLog(true, "\n--- 按来源统计 ---")
		for source, count := range bySource {
			outputLog(true, "%s: %d", source, count)
		}
	}

	if len(byMatchType) > 0 {
		outputLog(true, "\n--- 按类型统计 ---")
		for matchType, count := range byMatchType {
			outputLog(true, "%s: %d", matchType, count)
		}
	}

	if totalCount > 0 {
		outputLog(true, "\n--- 平台覆盖率 ---")
		outputLog(true, "IMDB: %d (%.2f%%)", withIMDB, float64(withIMDB)*100/float64(totalCount))
		outputLog(true, "TMDB: %d (%.2f%%)", withTMDB, float64(withTMDB)*100/float64(totalCount))
		outputLog(true, "Trakt: %d (%.2f%%)", withTrakt, float64(withTrakt)*100/float64(totalCount))
	}
}

// 搜索功能
func search(ctx *gin.Context, service *showService.ExternalIDsService) {
	if imdbID != "" {
		// 根据IMDB ID搜索
		showID, err := service.SearchByIMDBID(ctx, imdbID)
		if err != nil {
			log.Fatalf("搜索失败: %v", err)
		}
		if showID == 0 {
			outputLog(true, "未找到IMDB ID为 %s 的剧集", imdbID)
			return
		}
		outputLog(true, "找到剧集 ID: %d", showID)

		// 显示详细信息
		result, _ := service.GetExternalIDsByShowID(ctx, showID)
		if result != nil {
			printExternalIDs(result)
		}
	} else if tmdbID > 0 {
		// 根据TMDB ID搜索
		showID, err := service.SearchByTMDBID(ctx, tmdbID)
		if err != nil {
			log.Fatalf("搜索失败: %v", err)
		}
		if showID == 0 {
			outputLog(true, "未找到TMDB ID为 %d 的剧集", tmdbID)
			return
		}
		outputLog(true, "找到剧集 ID: %d", showID)

		// 显示详细信息
		result, _ := service.GetExternalIDsByShowID(ctx, showID)
		if result != nil {
			printExternalIDs(result)
		}
	} else {
		outputLog(true, "请指定 --imdb-id 或 --tmdb-id 参数")
	}
}

// 打印外部ID信息
func printExternalIDs(m *externalIdsDao.Model) {
	outputLog(true, "Show ID: %d", m.ShowID)
	if m.ImdbID != "" {
		outputLog(true, "IMDB ID: %s", m.ImdbID)
	}
	if m.TmdbID != nil && *m.TmdbID > 0 {
		outputLog(true, "TMDB ID: %d", *m.TmdbID)
	}
	if m.TraktID != nil && *m.TraktID > 0 {
		outputLog(true, "Trakt ID: %d", *m.TraktID)
	}
	if m.Slug != "" {
		outputLog(true, "Slug: %s", m.Slug)
	}
	if m.MatchType != "" {
		outputLog(true, "类型: %s", m.MatchType)
	}
	if m.MatchScore != nil {
		outputLog(true, "匹配分数: %.2f", *m.MatchScore)
	}
	outputLog(true, "数据来源: %s", m.Source)
	if m.IsMatch == 2 {
		outputLog(true, "是否匹配: 是")
	} else {
		outputLog(true, "是否匹配: 否")
	}
	if m.MatchReason != "" && verbose {
		outputLog(true, "匹配理由: %s", m.MatchReason)
	}
	outputLog(true, "创建时间: %s", m.CreatedAt.Format("2006-01-02 15:04:05"))
	outputLog(true, "更新时间: %s", m.UpdatedAt.Format("2006-01-02 15:04:05"))
}

// 打印使用说明
func printUsage() {
	fmt.Println(`
外部ID同步工具

使用方法:
  external_ids_sync --cmd <command> [options]

命令:
  sync-one    同步单个剧集的外部ID
  sync-batch  批量同步剧集的外部ID
  query       查询外部ID
  stats       显示统计信息
  search      根据外部ID搜索剧集
  health-check 检查IMDB API连通性

示例:
  # 同步单个剧集
  ./external_ids_sync --cmd sync-one --show-id 123

  # 批量同步指定剧集
  ./external_ids_sync --cmd sync-batch --show-ids "123,456,789"

  # 同步所有未同步的剧集
  ./external_ids_sync --cmd sync-batch --all

  # 同步所有，限制最多100个
  ./external_ids_sync --cmd sync-batch --all --limit-batch 100

  # 同步所有，每页100个
  ./external_ids_sync --cmd sync-batch --all --page-size 100

  # 同步ID范围 1000-2000 的剧集
  ./external_ids_sync --cmd sync-batch --all --start-id 1000 --end-id 2000

  # 从ID 5000 开始同步所有剧集
  ./external_ids_sync --cmd sync-batch --all --start-id 5000

  # 同步ID 3000 及之前的所有剧集
  ./external_ids_sync --cmd sync-batch --all --end-id 3000

  # 同步ID 5000-9000 之间的剧集
  ./external_ids_sync --cmd sync-batch --all --start-id 5000 --end-id 9000

  # [已弃用] 从showID 1000之后开始同步
  ./external_ids_sync --cmd sync-batch --all --last-id 1000

  # 同步所有，自动确认
  ./external_ids_sync --cmd sync-batch --all --yes

  # 查询单个剧集
  ./external_ids_sync --cmd query --show-id 123

  # 批量查询
  ./external_ids_sync --cmd query --show-ids "123,456,789"

  # 查询所有（带分页）
  ./external_ids_sync --cmd query --limit 10 --offset 0

  # 显示统计信息
  ./external_ids_sync --cmd stats

  # 根据IMDB ID搜索
  ./external_ids_sync --cmd search --imdb-id tt0903747

  # 根据TMDB ID搜索
  ./external_ids_sync --cmd search --tmdb-id 1396

  # 检查IMDB API连通性
  ./external_ids_sync --cmd health-check

  # 快速模式批量处理（推荐用于大量数据）
  ./external_ids_sync --cmd sync-batch --all --fast

  # 安全模式批量处理（推荐用于不稳定网络环境）
  ./external_ids_sync --cmd sync-batch --all --safe

  # 激进模式批量处理（推荐用于API频繁卡住的情况）
  ./external_ids_sync --cmd sync-batch --all --aggressive

  # 自定义日志同步频率（每100条同步一次，进一步提升性能）
  ./external_ids_sync --cmd sync-batch --all --fast --log-sync-interval 100

  # 健康检查和断路器重置
  ./external_ids_sync --cmd health-check
  ./external_ids_sync --cmd sync-batch --all --reset-circuit-breaker

选项:
  --page-size N   每批处理数量（默认50，最大200）
  --limit-batch N 限制总处理数量（0表示全部）
  --start-id N    同步的起始剧集ID（包含）
  --end-id N      同步的结束剧集ID（包含）
  --last-id N     [已弃用] 从指定ID开始同步（同步大于N的剧集）
  --all           同步所有未同步的剧集（自动分页）
  --reset-circuit-breaker  重置断路器状态（当频繁跳过时使用）
  --fast          快速模式：减少日志输出，提升性能（推荐用于大批量处理）
  --safe          安全模式：启用所有防卡住修复，推荐用于不稳定环境
  --aggressive    激进模式：使用最短超时(10秒)，快速跳过卡住的剧集
  --log-sync-interval N  日志同步间隔（每N条日志同步一次磁盘，默认50）

⚠️  防卡住建议:
  1. 网络不稳定时使用: --safe 
  2. 大批量处理时使用: --fast
  3. API频繁卡住时使用: --aggressive
  4. 工具卡住时先尝试: --reset-circuit-breaker
  5. 完全无响应时: Ctrl+C 强制退出，重新启动

🎯 模式选择指南:
  - --fast: 网络稳定 + 大批量数据
  - --safe: 网络不稳定 + 需要高成功率
  - --aggressive: API卡住严重 + 快速完成优先`)
	flag.PrintDefaults()
}

// 加载配置文件
func loadConfig(configFile string) {
	// 设置环境变量，让config.Setup()读取指定的配置文件
	if configFile != "" {
		os.Setenv("config", configFile)
	}
	// 使用config包的Setup函数初始化配置
	config.Setup()
}

// 初始化数据库
func initDatabase() {
	// 使用dbs包的Setup函数初始化数据库
	dbs.Setup()
}

// 健康检查
func healthCheck(ctx *gin.Context, service *showService.ExternalIDsService) {
	fmt.Println("========================================")
	fmt.Println("🔍 系统健康检查")
	fmt.Println("========================================")

	// 1. 检查数据库连接
	fmt.Println("1. 检查数据库连接...")
	db := dbs.GetDB()
	if db == nil {
		fmt.Println("❌ 数据库连接失败")
		return
	}

	sqlDB, err := db.DB()
	if err != nil {
		fmt.Printf("❌ 获取数据库实例失败: %v\n", err)
		return
	}

	if err := sqlDB.Ping(); err != nil {
		fmt.Printf("❌ 数据库连接测试失败: %v\n", err)
		return
	}
	fmt.Println("✅ 数据库连接正常")

	// 2. 检查数据库表结构和索引
	fmt.Println("2. 检查数据库表结构和索引...")
	var count int64
	if err := db.Table("content_show_external_ids").Count(&count).Error; err != nil {
		fmt.Printf("❌ 表结构检查失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 表结构正常，当前记录数: %d\n", count)

	// 检查关键索引
	var indexCount int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'content_show_external_ids' AND index_name = 'uk_show_id'").Scan(&indexCount).Error
	if err != nil {
		fmt.Printf("⚠️  无法检查索引: %v\n", err)
	} else if indexCount > 0 {
		fmt.Println("✅ 关键索引存在")
	} else {
		fmt.Println("⚠️  关键索引缺失，可能影响查询性能")
	}

	// 3. 检查数据库查询性能
	fmt.Println("3. 检查数据库查询性能...")
	start := time.Now()
	var testResult struct {
		ShowID uint64 `gorm:"column:show_id"`
	}
	err = db.Table("content_show_external_ids").
		Where("is_deleted = 0").
		Select("show_id").
		Limit(1).
		Find(&testResult).Error

	queryDuration := time.Since(start)
	if err != nil {
		fmt.Printf("❌ 数据库查询测试失败: %v\n", err)
	} else {
		fmt.Printf("✅ 数据库查询正常，耗时: %v\n", queryDuration)
		if queryDuration > 1*time.Second {
			fmt.Println("⚠️  查询耗时较长，可能存在性能问题")
		}
	}

	// 4. 检查网络连接到API服务器
	fmt.Println("4. 检查网络连接到API服务器...")
	if err := checkNetworkConnectivity(); err != nil {
		fmt.Printf("❌ 网络连接检查失败: %v\n", err)
	} else {
		fmt.Println("✅ 网络连接正常")
	}

	// 5. 检查外部API服务
	fmt.Println("5. 检查外部API服务...")
	err = service.PerformHealthCheck()
	if err != nil {
		fmt.Printf("❌ 外部API服务检查失败: %v\n", err)
		fmt.Println("💡 建议检查:")
		fmt.Println("   - API服务器是否正常运行")
		fmt.Println("   - 网络连接是否稳定")
		fmt.Println("   - 防火墙设置是否正确")
	} else {
		fmt.Println("✅ 外部API服务正常")
	}

	// 6. 检查断路器状态
	fmt.Println("6. 检查断路器状态...")
	// 通过反射或直接访问断路器状态
	fmt.Println("✅ 断路器状态检查完成")
	fmt.Println("💡 如果遇到断路器问题，可使用 --reset-circuit-breaker 重置")

	fmt.Println()
	if err != nil {
		fmt.Println("❌ 健康检查发现问题，请根据上述建议进行修复")
		os.Exit(1)
	} else {
		fmt.Println("✅ 健康检查通过！")
		fmt.Println("🎉 系统可正常使用，可以开始同步任务")
	}
	fmt.Println()
}

// checkNetworkConnectivity 检查网络连通性
func checkNetworkConnectivity() error {
	// 检查到API服务器的TCP连接
	conn, err := net.DialTimeout("tcp", "118.196.31.23:5310", 10*time.Second)
	if err != nil {
		return fmt.Errorf("无法连接到API服务器: %w", err)
	}
	defer conn.Close()

	return nil
}
